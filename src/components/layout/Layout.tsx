'use client';

import React from 'react';
import Header from './Header';
import SimpleSidebar from '../navigation/SimpleSidebar';

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <div className="flex min-h-screen flex-col overflow-hidden">
      <Header />
      <div className="flex flex-1 pt-16">
        <SimpleSidebar />
        <main className="flex-1 transition-all duration-300 w-full overflow-y-auto h-[calc(100vh-64px)]">
          <div className="container mx-auto px-4 py-8 pb-16 md:px-6 lg:px-8 min-h-[calc(100vh-64px)]">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
