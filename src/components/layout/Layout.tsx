'use client';

import React, { useEffect } from 'react';
import Header from './Header';
import EnhancedSidebar from '../navigation/EnhancedSidebar';
// import SimpleSidebar from '../navigation/SimpleSidebar';
// import QuickActions from '../navigation/QuickActions';
import { NavigationProvider } from '@/contexts/NavigationContext';

export default function Layout({ children }: { children: React.ReactNode }) {
  // Simplified layout - removed old event listeners that might conflict with new navigation

  return (
    <NavigationProvider>
      <div className="flex min-h-screen flex-col overflow-hidden">
        <Header />
        <div className="flex flex-1 pt-16">
          <EnhancedSidebar />
          <main
            className="flex-1 transition-all duration-300 w-full overflow-y-auto h-[calc(100vh-64px)]"
          >
            <div className="container mx-auto px-4 py-8 pb-16 md:px-6 lg:px-8 min-h-[calc(100vh-64px)]">
              {children}
            </div>
          </main>
        </div>
      </div>
    </NavigationProvider>
  );
}
