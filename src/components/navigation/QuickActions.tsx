'use client';

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPlus,
  faUpload,
  faMicrophone,
  faExchange,
  faSave,
  faSearch,
  faThumbtack,
  faTrash,
  faEllipsisV
} from '@fortawesome/free-solid-svg-icons';
import { useQuickActions } from '@/contexts/NavigationContext';
import { QuickAction, NavigationContext } from '@/types/navigation';

interface QuickActionsProps {
  className?: string;
}

// Define default quick actions based on context (moved outside component)
const getDefaultActions = (context: NavigationContext): QuickAction[] => {
    const baseActions: QuickAction[] = [
      {
        id: 'new-chat',
        icon: 'plus',
        label: 'New Chat',
        action: () => {
          // TODO: Implement new chat creation
          console.log('Creating new chat...');
        },
        contexts: ['*'],
        position: 'floating',
        priority: 1
      },
      {
        id: 'global-search',
        icon: 'search',
        label: 'Search',
        action: () => {
          // TODO: Implement global search
          console.log('Opening global search...');
        },
        contexts: ['*'],
        position: 'floating',
        priority: 2
      }
    ];

    // Context-specific actions
    if (context.currentArea === 'knowledge' || context.isInKnowledgeArea) {
      baseActions.push({
        id: 'upload-document',
        icon: 'upload',
        label: 'Upload Document',
        action: () => {
          // TODO: Implement document upload
          console.log('Opening document upload...');
        },
        contexts: ['knowledge', 'chat'],
        position: 'floating',
        priority: 3
      });
    }

    if (context.currentArea === 'conversations' || context.hasActiveSession) {
      baseActions.push(
        {
          id: 'voice-mode',
          icon: 'microphone',
          label: 'Voice Chat',
          action: () => {
            // TODO: Implement voice chat
            console.log('Starting voice chat...');
          },
          contexts: ['conversations'],
          position: 'floating',
          priority: 4
        },
        {
          id: 'switch-model',
          icon: 'exchange',
          label: 'Switch Model',
          action: () => {
            // TODO: Implement model switching
            console.log('Opening model selector...');
          },
          contexts: ['conversations'],
          position: 'floating',
          priority: 5
        },
        {
          id: 'save-session',
          icon: 'save',
          label: 'Save Session',
          action: () => {
            // TODO: Implement session saving
            console.log('Saving current session...');
          },
          contexts: ['conversations'],
          position: 'floating',
          priority: 6
        }
      );
    }

    if (context.activeChat) {
      baseActions.push(
        {
          id: 'pin-chat',
          icon: 'thumbtack',
          label: 'Pin Chat',
          action: () => {
            // TODO: Implement chat pinning
            console.log('Pinning chat...');
          },
          contexts: ['conversations'],
          position: 'floating',
          priority: 7
        }
      );
    }

    return baseActions;
};

export default function QuickActions({ className = '' }: QuickActionsProps) {
  const { quickActions, executeAction, context } = useQuickActions();
  const [isExpanded, setIsExpanded] = useState(false);
  const [visibleActions, setVisibleActions] = useState<QuickAction[]>([]);

  // Update visible actions based on context
  useEffect(() => {
    const defaultActions = getDefaultActions(context);
    const contextActions = defaultActions.filter(action => {
      if (action.contexts.includes('*')) return true;
      return action.contexts.includes(context.currentArea);
    });

    // Merge with any custom quick actions and sort by priority
    const allActions = [...contextActions, ...quickActions]
      .sort((a, b) => a.priority - b.priority)
      .slice(0, 6); // Limit to 6 actions max

    setVisibleActions(allActions);
  }, [context.currentArea, context.hasActiveSession, context.isInKnowledgeArea, context.activeChat, quickActions]);

  const getIconComponent = (iconName: string) => {
    const iconMap: Record<string, any> = {
      plus: faPlus,
      upload: faUpload,
      microphone: faMicrophone,
      exchange: faExchange,
      save: faSave,
      search: faSearch,
      thumbtack: faThumbtack,
      trash: faTrash
    };

    return iconMap[iconName] || faPlus;
  };

  const handleActionClick = (action: QuickAction) => {
    executeAction(action.id);
    setIsExpanded(false);
  };

  const primaryAction = visibleActions[0];
  const secondaryActions = visibleActions.slice(1);

  return (
    <div className={`fixed bottom-6 right-6 z-50 ${className}`}>
      <div className="flex flex-col items-end space-y-3">
        {/* Secondary actions (shown when expanded) */}
        {isExpanded && secondaryActions.length > 0 && (
          <div className="flex flex-col items-end space-y-2 animate-in slide-in-from-bottom-2 duration-200">
            {secondaryActions.map((action) => (
              <button
                key={action.id}
                onClick={() => handleActionClick(action)}
                className="group relative flex h-12 w-12 items-center justify-center rounded-full bg-white shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 border border-gray-200 dark:bg-gray-800 dark:border-gray-700"
                title={action.label}
              >
                <FontAwesomeIcon
                  icon={getIconComponent(action.icon)}
                  className="h-5 w-5 text-gray-600 dark:text-gray-300"
                />

                {/* Tooltip */}
                <div className="absolute right-14 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                  <div className="bg-gray-900 text-white text-sm px-2 py-1 rounded whitespace-nowrap">
                    {action.label}
                  </div>
                  <div className="absolute left-full top-1/2 -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
                </div>

                {/* Badge */}
                {action.badge && (
                  <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {action.badge}
                  </div>
                )}
              </button>
            ))}
          </div>
        )}

        {/* Primary action button */}
        {primaryAction && (
          <div className="relative">
            <button
              onClick={() => handleActionClick(primaryAction)}
              className="flex h-14 w-14 items-center justify-center rounded-full bg-blue-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 hover:bg-blue-700"
              title={primaryAction.label}
            >
              <FontAwesomeIcon
                icon={getIconComponent(primaryAction.icon)}
                className="h-6 w-6"
              />

              {/* Badge */}
              {primaryAction.badge && (
                <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {primaryAction.badge}
                </div>
              )}
            </button>

            {/* Expand/collapse button for secondary actions */}
            {secondaryActions.length > 0 && (
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="absolute -top-2 -left-2 flex h-6 w-6 items-center justify-center rounded-full bg-gray-600 text-white shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105"
                title={isExpanded ? 'Collapse' : 'More actions'}
              >
                <FontAwesomeIcon
                  icon={faEllipsisV}
                  className={`h-3 w-3 transition-transform duration-200 ${
                    isExpanded ? 'rotate-90' : ''
                  }`}
                />
              </button>
            )}
          </div>
        )}
      </div>

      {/* Backdrop for mobile */}
      {isExpanded && (
        <div
          className="fixed inset-0 bg-black bg-opacity-20 md:hidden -z-10"
          onClick={() => setIsExpanded(false)}
        />
      )}
    </div>
  );
}
