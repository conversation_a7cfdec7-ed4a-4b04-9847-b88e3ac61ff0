'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faHome,
  faRobot,
  faComments,
  faBook,
  faCog,
  faUserShield,
  faChevronDown,
  faChevronRight,
  faSearch,
  faBars,
  faHeart,
  faGraduationCap,
  faUser,
  faChartLine,
  faUsers,
  faVideo,
  faNewspaper,
  faFolder,
  faUpload
} from '@fortawesome/free-solid-svg-icons';

interface SidebarItem {
  id: string;
  label: string;
  icon: any;
  path: string;
  children?: SidebarItem[];
}

interface SidebarSection {
  id: string;
  title: string;
  icon: any;
  children: SidebarItem[];
}

const sidebarSections: SidebarSection[] = [
  {
    id: 'dashboard',
    title: 'Dashboard',
    icon: faHome,
    children: [
      { id: 'home', label: 'Home', icon: faHome, path: '/' }
    ]
  },
  {
    id: 'agents',
    title: 'AI Agents',
    icon: faRobot,
    children: [
      { id: 'browse', label: 'Browse Agents', icon: faRobot, path: '/browse' },
      { id: 'popular', label: 'Popular', icon: faChartLine, path: '/popular' },
      { id: 'favorites', label: 'Favorites', icon: faHeart, path: '/favorites' }
    ]
  },
  {
    id: 'conversations',
    title: 'Conversations',
    icon: faComments,
    children: [
      { id: 'chat', label: 'New Chat', icon: faComments, path: '/conversations' }
    ]
  },
  {
    id: 'knowledge',
    title: 'Knowledge & Learning',
    icon: faBook,
    children: [
      { id: 'knowledge', label: 'Knowledge Base', icon: faBook, path: '/knowledge' },
      { id: 'learning', label: 'Learning Hub', icon: faGraduationCap, path: '/learning' },
      { id: 'learning-articles', label: 'Articles', icon: faNewspaper, path: '/learning/articles' },
      { id: 'learning-videos', label: 'Videos', icon: faVideo, path: '/learning/videos' },
      { id: 'learning-blog', label: 'Blog', icon: faNewspaper, path: '/learning/blog' },
      { id: 'learning-resources', label: 'Resources', icon: faFolder, path: '/learning/resources' }
    ]
  },
  {
    id: 'user',
    title: 'User',
    icon: faUser,
    children: [
      { id: 'profile', label: 'Profile', icon: faUser, path: '/profile' }
    ]
  },
  {
    id: 'tools',
    title: 'Tools & Settings',
    icon: faCog,
    children: [
      { id: 'tools', label: 'Tools', icon: faCog, path: '/tools' }
    ]
  },
  {
    id: 'admin',
    title: 'Admin',
    icon: faUserShield,
    children: [
      { id: 'admin-dashboard', label: 'Admin Dashboard', icon: faUserShield, path: '/admin' },
      { id: 'admin-users', label: 'User Management', icon: faUsers, path: '/admin/users' },
      { id: 'admin-analytics', label: 'Analytics', icon: faChartLine, path: '/admin/analytics' },
      { id: 'admin-ai-org', label: 'AI Organization', icon: faRobot, path: '/admin/ai-org' }
    ]
  }
];

export default function SimpleSidebar() {
  const pathname = usePathname();
  const [collapsed, setCollapsed] = useState(false);
  const [collapsedSections, setCollapsedSections] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  const toggleSidebar = () => setCollapsed(!collapsed);

  const toggleSection = (sectionId: string) => {
    setCollapsedSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const isActive = (path: string) => {
    if (pathname === path) return true;
    // Handle nested routes - if current path starts with the nav path (but not root)
    if (path !== '/' && pathname.startsWith(path)) return true;
    return false;
  };

  const isSectionActive = (section: SidebarSection) =>
    section.children.some(item => isActive(item.path));

  const filteredSections = searchQuery
    ? sidebarSections.map(section => ({
        ...section,
        children: section.children.filter(item =>
          item.label.toLowerCase().includes(searchQuery.toLowerCase())
        )
      })).filter(section => section.children.length > 0)
    : sidebarSections;

  return (
    <aside className={`fixed left-0 z-50 flex flex-col h-[calc(100vh-64px)] transition-all duration-300 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 ${
      collapsed ? 'w-16' : 'w-80'
    }`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-800">
        {!collapsed && (
          <div className="flex items-center space-x-2">
            <FontAwesomeIcon icon={faRobot} className="h-6 w-6 text-blue-600" />
            <span className="text-lg font-semibold text-gray-900 dark:text-white">Navigation</span>
          </div>
        )}
        <button
          onClick={toggleSidebar}
          className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
        >
          <FontAwesomeIcon icon={faBars} className="h-4 w-4 text-gray-600 dark:text-gray-400" />
        </button>
      </div>

      {/* Search */}
      {!collapsed && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-800">
          <div className="relative">
            <FontAwesomeIcon
              icon={faSearch}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
            />
            <input
              type="text"
              placeholder="Search..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 overflow-y-auto p-4 space-y-2">
        {filteredSections.map((section) => {
          const sectionCollapsed = collapsedSections.includes(section.id);
          const sectionActive = isSectionActive(section);

          return (
            <div key={section.id} className="space-y-1">
              {/* Section Header */}
              <button
                onClick={() => !collapsed && toggleSection(section.id)}
                className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors ${
                  sectionActive
                    ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <FontAwesomeIcon icon={section.icon} className="h-5 w-5" />
                  {!collapsed && <span className="font-medium">{section.title}</span>}
                </div>
                {!collapsed && (
                  <FontAwesomeIcon
                    icon={sectionCollapsed ? faChevronRight : faChevronDown}
                    className="h-4 w-4"
                  />
                )}
              </button>

              {/* Section Items */}
              {(!sectionCollapsed || collapsed) && (
                <div className={collapsed ? 'space-y-1' : 'ml-4 space-y-1'}>
                  {section.children.map((item) => (
                    <Link
                      key={item.id}
                      href={item.path}
                      className={`flex items-center space-x-3 p-2 rounded-lg transition-colors ${
                        isActive(item.path)
                          ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                          : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800/50'
                      }`}
                    >
                      <FontAwesomeIcon icon={item.icon} className="h-4 w-4" />
                      {!collapsed && <span>{item.label}</span>}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </nav>
    </aside>
  );
}
