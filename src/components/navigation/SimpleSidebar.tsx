'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faHome, faRobot, faComments, faBook, faCog } from '@fortawesome/free-solid-svg-icons';

export default function SimpleSidebar() {
  const pathname = usePathname();

  const menuItems = [
    { id: 'home', label: 'Home', icon: faHome, path: '/' },
    { id: 'agents', label: 'Agents', icon: faRobot, path: '/browse' },
    { id: 'conversations', label: 'Conversations', icon: faComments, path: '/conversations' },
    { id: 'knowledge', label: 'Knowledge', icon: faBook, path: '/knowledge' },
    { id: 'tools', label: 'Tools', icon: faCog, path: '/tools' }
  ];

  return (
    <aside className="fixed left-0 z-50 flex flex-col h-[calc(100vh-64px)] w-80 transition-all duration-300 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800">
      {/* Sidebar Header */}
      <div className="flex h-16 items-center justify-between border-b border-gray-200/30 px-4 dark:border-gray-800/30 mt-2">
        <span className="text-lg font-semibold text-gray-900 dark:text-white">
          AI Hub
        </span>
      </div>

      {/* Navigation Content */}
      <div className="flex-1 overflow-y-auto p-4">
        <nav className="space-y-2">
          {menuItems.map((item) => (
            <Link
              key={item.id}
              href={item.path}
              className={`flex items-center px-3 py-2 text-sm rounded-md transition-colors ${
                pathname === item.path
                  ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'
                  : 'text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800'
              }`}
            >
              <FontAwesomeIcon icon={item.icon} className="h-4 w-4 mr-3" />
              <span>{item.label}</span>
            </Link>
          ))}
        </nav>
      </div>
    </aside>
  );
}
