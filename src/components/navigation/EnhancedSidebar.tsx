'use client';

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faHome,
  faRobot,
  faComments,
  faBook,
  faCog,
  faUserShield,
  faChevronDown,
  faChevronRight,
  faBars,
  faPlus,
  faSearch,
  faTableCells,
  faChartLine,
  faHeart,
  faHistory,
  faThumbtack,
  faFolder,
  faFile,
  faUpload,
  faGraduationCap,
  faVideo,
  faNewspaper,
  faBlog,
  faToolbox,
  faKey,
  faLink,
  faMicrophone,
  faGear,
  faChartBar,
  faUsers,
  faSitemap,
  faServer
} from '@fortawesome/free-solid-svg-icons';
import { useNavigation } from '@/contexts/NavigationContext';
import { SidebarSection, SidebarItem } from '@/types/navigation';
import RenderTracker from '@/components/debug/RenderTracker';

// Custom hook to stabilize array references
function useStableArray<T>(array: T[]): T[] {
  const stableRef = useRef<T[]>(array);

  // Only update if the content actually changed
  if (array.length !== stableRef.current.length ||
      !array.every((item, index) => item === stableRef.current[index])) {
    stableRef.current = array;
  }

  return stableRef.current;
}

export default function EnhancedSidebar() {
  const pathname = usePathname();
  const { state, toggleSection, toggleSidebar } = useNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredSections, setFilteredSections] = useState<SidebarSection[]>([]);

  // Stabilize the collapsedSections array to prevent unnecessary re-renders
  const stableCollapsedSections = useStableArray(state.collapsedSections);

  // Memoize sidebar sections to prevent recreation on every render
  const sidebarSections: SidebarSection[] = useMemo(() => [
    {
      id: 'dashboard',
      title: 'Dashboard',
      icon: 'home',
      isCollapsed: false,
      order: 1,
      children: [
        {
          id: 'home',
          label: 'Home',
          icon: 'home',
          path: '/'
        }
      ]
    },
    {
      id: 'agents',
      title: 'Agents',
      icon: 'robot',
      isCollapsed: stableCollapsedSections.includes('agents'),
      order: 2,
      children: [
        {
          id: 'browse-agents',
          label: 'Browse All',
          icon: 'table-cells',
          path: '/browse'
        },
        {
          id: 'popular-agents',
          label: 'Popular',
          icon: 'chart-line',
          path: '/popular'
        },
        {
          id: 'favorite-agents',
          label: 'Favorites',
          icon: 'heart',
          path: '/favorites',
          badge: '3' // Example badge
        },
        {
          id: 'new-agent-chat',
          label: 'New Agent Chat',
          icon: 'plus',
          path: '/agent/new',
          isNew: true
        }
      ]
    },
    {
      id: 'conversations',
      title: 'Conversations',
      icon: 'comments',
      isCollapsed: stableCollapsedSections.includes('conversations'),
      badgeCount: 2, // Example: 2 unread conversations
      order: 3,
      children: [
        {
          id: 'active-sessions',
          label: 'Active Sessions',
          icon: 'comments',
          path: '/conversations/active',
          badge: '2'
        },
        {
          id: 'chat-history',
          label: 'Chat History',
          icon: 'history',
          path: '/conversations/history'
        },
        {
          id: 'pinned-chats',
          label: 'Pinned Chats',
          icon: 'thumbtack',
          path: '/conversations/pinned'
        },
        {
          id: 'chat-folders',
          label: 'Chat Folders',
          icon: 'folder',
          path: '/conversations/folders'
        },
        {
          id: 'new-conversation',
          label: 'New Conversation',
          icon: 'plus',
          path: '/conversations/new',
          isNew: true
        }
      ]
    },
    {
      id: 'knowledge',
      title: 'Knowledge Base',
      icon: 'book',
      isCollapsed: stableCollapsedSections.includes('knowledge'),
      order: 4,
      children: [
        {
          id: 'my-documents',
          label: 'My Documents',
          icon: 'file',
          path: '/knowledge/documents'
        },
        {
          id: 'upload-files',
          label: 'Upload Files',
          icon: 'upload',
          path: '/knowledge/upload',
          isNew: true
        },
        {
          id: 'learning-hub',
          label: 'Learning Hub',
          icon: 'graduation-cap',
          path: '/learning',
          children: [
            {
              id: 'videos',
              label: 'Videos',
              icon: 'video',
              path: '/learning/videos'
            },
            {
              id: 'articles',
              label: 'Articles',
              icon: 'newspaper',
              path: '/learning/articles'
            },
            {
              id: 'blog',
              label: 'Blog Posts',
              icon: 'blog',
              path: '/learning/blog'
            },
            {
              id: 'resources',
              label: 'Resources',
              icon: 'toolbox',
              path: '/learning/resources'
            }
          ]
        },
        {
          id: 'search-knowledge',
          label: 'Search Knowledge',
          icon: 'search',
          path: '/knowledge/search'
        }
      ]
    },
    {
      id: 'tools',
      title: 'Tools & Settings',
      icon: 'cog',
      isCollapsed: stableCollapsedSections.includes('tools'),
      order: 5,
      children: [
        {
          id: 'ai-models',
          label: 'AI Models',
          icon: 'robot',
          path: '/tools/models'
        },
        {
          id: 'integrations',
          label: 'Integrations',
          icon: 'link',
          path: '/tools/integrations'
        },
        {
          id: 'api-keys',
          label: 'API Keys',
          icon: 'key',
          path: '/tools/api-keys'
        },
        {
          id: 'voice-audio',
          label: 'Voice & Audio',
          icon: 'microphone',
          path: '/tools/voice'
        },
        {
          id: 'preferences',
          label: 'Preferences',
          icon: 'gear',
          path: '/tools/preferences'
        },
        {
          id: 'usage-analytics',
          label: 'Usage Analytics',
          icon: 'chart-bar',
          path: '/tools/analytics'
        }
      ]
    }
  ], [stableCollapsedSections]);

  // Memoize admin section conditionally
  const adminSection: SidebarSection = useMemo(() => ({
    id: 'admin',
    title: 'Admin',
    icon: 'user-shield',
    isCollapsed: stableCollapsedSections.includes('admin'),
    order: 6,
    permissions: ['admin'],
    children: [
      {
        id: 'user-management',
        label: 'User Management',
        icon: 'users',
        path: '/admin/users'
      },
      {
        id: 'ai-organization',
        label: 'AI Organization',
        icon: 'sitemap',
        path: '/admin/ai-org'
      },
      {
        id: 'system-settings',
        label: 'System Settings',
        icon: 'server',
        path: '/admin/settings'
      },
      {
        id: 'platform-analytics',
        label: 'Platform Analytics',
        icon: 'chart-bar',
        path: '/admin/analytics'
      }
    ]
  }), [stableCollapsedSections]);

  // Get icon component
  const getIconComponent = (iconName: string) => {
    const iconMap: Record<string, any> = {
      home: faHome,
      robot: faRobot,
      comments: faComments,
      book: faBook,
      cog: faCog,
      'user-shield': faUserShield,
      'table-cells': faTableCells,
      'chart-line': faChartLine,
      heart: faHeart,
      history: faHistory,
      thumbtack: faThumbtack,
      folder: faFolder,
      file: faFile,
      upload: faUpload,
      'graduation-cap': faGraduationCap,
      video: faVideo,
      newspaper: faNewspaper,
      blog: faBlog,
      toolbox: faToolbox,
      key: faKey,
      link: faLink,
      microphone: faMicrophone,
      gear: faGear,
      'chart-bar': faChartBar,
      users: faUsers,
      sitemap: faSitemap,
      server: faServer,
      search: faSearch,
      plus: faPlus
    };

    return iconMap[iconName] || faHome;
  };

  // Filter sections based on search
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredSections([...sidebarSections, adminSection]);
      return;
    }

    const filtered = sidebarSections.map(section => ({
      ...section,
      children: section.children.filter(item =>
        item.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (item.children && item.children.some(child =>
          child.label.toLowerCase().includes(searchQuery.toLowerCase())
        ))
      )
    })).filter(section => section.children.length > 0);

    setFilteredSections(filtered);
  }, [searchQuery, sidebarSections, adminSection]);

  const handleSectionToggle = useCallback((sectionId: string) => {
    toggleSection(sectionId);
  }, [toggleSection]);

  const isItemActive = useCallback((item: SidebarItem): boolean => {
    if (item.path === pathname) return true;
    if (item.children) {
      return item.children.some(child => child.path === pathname);
    }
    return false;
  }, [pathname]);

  return (
    <aside
      className={`fixed left-0 z-50 flex flex-col h-[calc(100vh-64px)] transition-all duration-300 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 ${
        state.sidebarCollapsed ? 'w-16' : 'w-80'
      } md:translate-x-0 ${
        typeof window !== 'undefined' && document.documentElement.classList.contains('sidebar-open')
          ? 'translate-x-0'
          : '-translate-x-full'
      }`}
    >
      <RenderTracker
        name="EnhancedSidebar"
        props={{
          pathname,
          collapsedSections: stableCollapsedSections,
          sidebarCollapsed: state.sidebarCollapsed,
          searchQuery
        }}
      />
      {/* Sidebar Header */}
      <div className="flex h-16 items-center justify-between border-b border-gray-200/30 px-4 dark:border-gray-800/30 mt-2">
        <span className={`text-lg font-semibold text-gray-900 dark:text-white whitespace-nowrap overflow-hidden ${state.sidebarCollapsed ? 'hidden' : ''}`}>
          AI Hub
        </span>

        <button
          type="button"
          className={`p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300 ${state.sidebarCollapsed ? 'mx-auto' : ''}`}
          onClick={toggleSidebar}
          aria-label="Toggle sidebar"
        >
          <FontAwesomeIcon icon={faBars} className="h-5 w-5" />
        </button>
      </div>

      {/* Search Bar */}
      {!state.sidebarCollapsed && (
        <div className="p-4 border-b border-gray-200/30 dark:border-gray-800/30">
          <div className="relative">
            <FontAwesomeIcon
              icon={faSearch}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
            />
            <input
              type="text"
              placeholder="Search navigation..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:border-gray-700 dark:bg-gray-800 dark:text-white"
            />
          </div>
        </div>
      )}

      {/* Navigation Content */}
      <div className="flex-1 overflow-y-auto p-4">
        <nav className="space-y-2">
          {filteredSections.map((section) => (
            <div key={section.id} className="space-y-1">
              {/* Section Header */}
              <button
                onClick={() => handleSectionToggle(section.id)}
                className={`w-full flex items-center justify-between px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800 rounded-md ${
                  state.sidebarCollapsed ? 'justify-center' : ''
                }`}
                title={state.sidebarCollapsed ? section.title : undefined}
              >
                <div className="flex items-center">
                  <FontAwesomeIcon
                    icon={getIconComponent(section.icon)}
                    className={`h-5 w-5 ${state.sidebarCollapsed ? '' : 'mr-3'}`}
                  />
                  {!state.sidebarCollapsed && (
                    <span className="flex-1 text-left">{section.title}</span>
                  )}
                </div>

                {!state.sidebarCollapsed && (
                  <div className="flex items-center space-x-2">
                    {section.badgeCount && (
                      <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full dark:bg-blue-900 dark:text-blue-200">
                        {section.badgeCount}
                      </span>
                    )}
                    <FontAwesomeIcon
                      icon={section.isCollapsed ? faChevronRight : faChevronDown}
                      className="h-3 w-3"
                    />
                  </div>
                )}
              </button>

              {/* Section Items */}
              {(!section.isCollapsed || state.sidebarCollapsed) && (
                <div className={`space-y-1 ${state.sidebarCollapsed ? '' : 'ml-4'}`}>
                  {section.children.map((item) => (
                    <div key={item.id}>
                      <Link
                        href={item.path}
                        className={`flex items-center px-3 py-2 text-sm rounded-md transition-colors ${
                          isItemActive(item)
                            ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'
                            : 'text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800'
                        } ${state.sidebarCollapsed ? 'justify-center' : ''}`}
                        title={state.sidebarCollapsed ? item.label : undefined}
                      >
                        {item.icon && (
                          <FontAwesomeIcon
                            icon={getIconComponent(item.icon)}
                            className={`h-4 w-4 ${state.sidebarCollapsed ? '' : 'mr-3'}`}
                          />
                        )}
                        {!state.sidebarCollapsed && (
                          <span className="flex-1">{item.label}</span>
                        )}
                        {!state.sidebarCollapsed && item.badge && (
                          <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full dark:bg-red-900 dark:text-red-200">
                            {item.badge}
                          </span>
                        )}
                        {!state.sidebarCollapsed && item.isNew && (
                          <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full dark:bg-green-900 dark:text-green-200">
                            New
                          </span>
                        )}
                      </Link>

                      {/* Sub-items */}
                      {!state.sidebarCollapsed && item.children && (
                        <div className="ml-6 space-y-1">
                          {item.children.map((subItem) => (
                            <Link
                              key={subItem.id}
                              href={subItem.path}
                              className={`flex items-center px-3 py-1 text-sm rounded-md transition-colors ${
                                pathname === subItem.path
                                  ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'
                                  : 'text-gray-500 hover:bg-gray-100 dark:text-gray-500 dark:hover:bg-gray-800'
                              }`}
                            >
                              {subItem.icon && (
                                <FontAwesomeIcon
                                  icon={getIconComponent(subItem.icon)}
                                  className="h-3 w-3 mr-2"
                                />
                              )}
                              <span>{subItem.label}</span>
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>
      </div>
    </aside>
  );
}
