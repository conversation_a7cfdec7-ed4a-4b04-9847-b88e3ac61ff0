'use client';

import { useEffect, useRef } from 'react';

interface RenderTrackerProps {
  name: string;
  props?: Record<string, any>;
}

export default function RenderTracker({ name, props = {} }: RenderTrackerProps) {
  const renderCount = useRef(0);
  const prevProps = useRef(props);

  renderCount.current += 1;

  useEffect(() => {
    console.log(`🔄 ${name} rendered ${renderCount.current} times`);
    
    if (renderCount.current > 10) {
      console.error(`⚠️ ${name} has rendered ${renderCount.current} times - possible infinite loop!`);
      console.log('Current props:', props);
      console.log('Previous props:', prevProps.current);
      
      // Check which props changed
      const changedProps: string[] = [];
      Object.keys(props).forEach(key => {
        if (props[key] !== prevProps.current[key]) {
          changedProps.push(key);
        }
      });
      
      if (changedProps.length > 0) {
        console.log('Changed props:', changedProps);
      }
    }
    
    prevProps.current = props;
  });

  return null;
}
