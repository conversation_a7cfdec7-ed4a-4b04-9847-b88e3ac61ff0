'use client';

import { useRouter, usePathname } from 'next/navigation';
import { useNavigation } from '@/contexts/NavigationContext';
import { useCallback } from 'react';

export function useNavigationHelpers() {
  const router = useRouter();
  const pathname = usePathname();
  const { state, updateContext, selectChat, selectDocument } = useNavigation();

  // Navigation helpers
  const navigateToChat = useCallback((chatId: string) => {
    selectChat(chatId);
    router.push(`/conversations/${chatId}`);
  }, [router, selectChat]);

  const navigateToAgent = useCallback((agentId: string) => {
    updateContext({ currentArea: 'agents' });
    router.push(`/agents/${agentId}`);
  }, [router, updateContext]);

  const navigateToDocument = useCallback((documentId: string) => {
    selectDocument(documentId);
    router.push(`/knowledge/documents/${documentId}`);
  }, [router, selectDocument]);

  const createNewChat = useCallback((agentId?: string) => {
    updateContext({ 
      currentArea: 'conversations',
      hasActiveSession: true 
    });
    
    if (agentId) {
      router.push(`/conversations/new?agent=${agentId}`);
    } else {
      router.push('/conversations/new');
    }
  }, [router, updateContext]);

  const uploadDocument = useCallback(() => {
    updateContext({ 
      currentArea: 'knowledge',
      isInKnowledgeArea: true 
    });
    router.push('/knowledge/upload');
  }, [router, updateContext]);

  const openVoiceChat = useCallback(() => {
    updateContext({ 
      currentArea: 'conversations',
      hasActiveSession: true 
    });
    router.push('/conversations/voice');
  }, [router, updateContext]);

  const switchModel = useCallback(() => {
    router.push('/tools/models');
  }, [router]);

  const openGlobalSearch = useCallback(() => {
    // TODO: Implement global search modal
    console.log('Opening global search...');
  }, []);

  // Check if current path matches a pattern
  const isCurrentPath = useCallback((path: string): boolean => {
    return pathname === path;
  }, [pathname]);

  const isCurrentArea = useCallback((area: string): boolean => {
    return state.context.currentArea === area;
  }, [state.context.currentArea]);

  // Get breadcrumb navigation
  const getBreadcrumbs = useCallback(() => {
    const segments = pathname.split('/').filter(Boolean);
    const breadcrumbs = [];

    let currentPath = '';
    for (const segment of segments) {
      currentPath += `/${segment}`;
      
      // Convert segment to readable label
      let label = segment.charAt(0).toUpperCase() + segment.slice(1);
      label = label.replace(/-/g, ' ');
      
      breadcrumbs.push({
        label,
        path: currentPath,
        isActive: currentPath === pathname
      });
    }

    return breadcrumbs;
  }, [pathname]);

  // Get current section info
  const getCurrentSection = useCallback(() => {
    if (pathname.startsWith('/browse') || pathname.startsWith('/popular') || pathname.startsWith('/favorites') || pathname.startsWith('/agents')) {
      return { id: 'agents', title: 'Agents', icon: 'robot' };
    } else if (pathname.startsWith('/conversations') || pathname.startsWith('/chat')) {
      return { id: 'conversations', title: 'Conversations', icon: 'comments' };
    } else if (pathname.startsWith('/knowledge') || pathname.startsWith('/documents') || pathname.startsWith('/learning')) {
      return { id: 'knowledge', title: 'Knowledge Base', icon: 'book' };
    } else if (pathname.startsWith('/tools') || pathname.startsWith('/settings')) {
      return { id: 'tools', title: 'Tools & Settings', icon: 'cog' };
    } else if (pathname.startsWith('/admin')) {
      return { id: 'admin', title: 'Admin', icon: 'user-shield' };
    } else {
      return { id: 'dashboard', title: 'Dashboard', icon: 'home' };
    }
  }, [pathname]);

  return {
    // Navigation actions
    navigateToChat,
    navigateToAgent,
    navigateToDocument,
    createNewChat,
    uploadDocument,
    openVoiceChat,
    switchModel,
    openGlobalSearch,
    
    // State helpers
    isCurrentPath,
    isCurrentArea,
    getBreadcrumbs,
    getCurrentSection,
    
    // Current state
    currentPath: pathname,
    currentArea: state.context.currentArea,
    hasActiveSession: state.context.hasActiveSession,
    isInKnowledgeArea: state.context.isInKnowledgeArea
  };
}

// Hook for quick actions with navigation helpers
export function useQuickActionsWithNavigation() {
  const helpers = useNavigationHelpers();
  
  const quickActionHandlers = {
    'new-chat': helpers.createNewChat,
    'upload-document': helpers.uploadDocument,
    'voice-mode': helpers.openVoiceChat,
    'switch-model': helpers.switchModel,
    'global-search': helpers.openGlobalSearch,
    'save-session': () => {
      // TODO: Implement session saving
      console.log('Saving current session...');
    },
    'pin-chat': () => {
      // TODO: Implement chat pinning
      console.log('Pinning current chat...');
    }
  };

  return {
    ...helpers,
    quickActionHandlers
  };
}
