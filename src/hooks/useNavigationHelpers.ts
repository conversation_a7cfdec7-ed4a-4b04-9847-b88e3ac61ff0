'use client';

import { useRouter, usePathname } from 'next/navigation';
import { useCallback } from 'react';

export function useNavigationHelpers() {
  const router = useRouter();
  const pathname = usePathname();

  // Navigation helpers - simplified without complex context
  const navigateToChat = useCallback((chatId: string) => {
    router.push(`/conversations/${chatId}`);
  }, [router]);

  const navigateToAgent = useCallback((agentId: string) => {
    router.push(`/agents/${agentId}`);
  }, [router]);

  const navigateToDocument = useCallback((documentId: string) => {
    router.push(`/knowledge/documents/${documentId}`);
  }, [router]);

  const createNewChat = useCallback((agentId?: string) => {
    if (agentId) {
      router.push(`/conversations/new?agent=${agentId}`);
    } else {
      router.push('/conversations/new');
    }
  }, [router]);

  const uploadDocument = useCallback(() => {
    router.push('/knowledge/upload');
  }, [router]);

  const openVoiceChat = useCallback(() => {
    router.push('/conversations/voice');
  }, [router]);

  const switchModel = useCallback(() => {
    router.push('/tools/models');
  }, [router]);

  const openGlobalSearch = useCallback(() => {
    // TODO: Implement global search modal
    console.log('Opening global search...');
  }, []);

  // Check if current path matches a pattern
  const isCurrentPath = useCallback((path: string): boolean => {
    return pathname === path;
  }, [pathname]);

  const isCurrentArea = useCallback((area: string): boolean => {
    // Simplified area detection based on pathname
    if (pathname.startsWith('/browse') || pathname.startsWith('/popular') || pathname.startsWith('/favorites') || pathname.startsWith('/agents')) {
      return area === 'agents';
    } else if (pathname.startsWith('/conversations') || pathname.startsWith('/chat')) {
      return area === 'conversations';
    } else if (pathname.startsWith('/knowledge') || pathname.startsWith('/documents') || pathname.startsWith('/learning')) {
      return area === 'knowledge';
    } else if (pathname.startsWith('/tools') || pathname.startsWith('/settings')) {
      return area === 'tools';
    } else if (pathname.startsWith('/admin')) {
      return area === 'admin';
    } else {
      return area === 'dashboard';
    }
  }, [pathname]);

  // Get breadcrumb navigation
  const getBreadcrumbs = useCallback(() => {
    const segments = pathname.split('/').filter(Boolean);
    const breadcrumbs = [];

    let currentPath = '';
    for (const segment of segments) {
      currentPath += `/${segment}`;

      // Convert segment to readable label
      let label = segment.charAt(0).toUpperCase() + segment.slice(1);
      label = label.replace(/-/g, ' ');

      breadcrumbs.push({
        label,
        path: currentPath,
        isActive: currentPath === pathname
      });
    }

    return breadcrumbs;
  }, [pathname]);

  // Get current section info
  const getCurrentSection = useCallback(() => {
    if (pathname.startsWith('/browse') || pathname.startsWith('/popular') || pathname.startsWith('/favorites') || pathname.startsWith('/agents')) {
      return { id: 'agents', title: 'Agents', icon: 'robot' };
    } else if (pathname.startsWith('/conversations') || pathname.startsWith('/chat')) {
      return { id: 'conversations', title: 'Conversations', icon: 'comments' };
    } else if (pathname.startsWith('/knowledge') || pathname.startsWith('/documents') || pathname.startsWith('/learning')) {
      return { id: 'knowledge', title: 'Knowledge Base', icon: 'book' };
    } else if (pathname.startsWith('/tools') || pathname.startsWith('/settings')) {
      return { id: 'tools', title: 'Tools & Settings', icon: 'cog' };
    } else if (pathname.startsWith('/admin')) {
      return { id: 'admin', title: 'Admin', icon: 'user-shield' };
    } else {
      return { id: 'dashboard', title: 'Dashboard', icon: 'home' };
    }
  }, [pathname]);

  // Get current area based on pathname
  const getCurrentArea = useCallback(() => {
    if (pathname.startsWith('/browse') || pathname.startsWith('/popular') || pathname.startsWith('/favorites') || pathname.startsWith('/agents')) {
      return 'agents';
    } else if (pathname.startsWith('/conversations') || pathname.startsWith('/chat')) {
      return 'conversations';
    } else if (pathname.startsWith('/knowledge') || pathname.startsWith('/documents') || pathname.startsWith('/learning')) {
      return 'knowledge';
    } else if (pathname.startsWith('/tools') || pathname.startsWith('/settings')) {
      return 'tools';
    } else if (pathname.startsWith('/admin')) {
      return 'admin';
    } else {
      return 'dashboard';
    }
  }, [pathname]);

  return {
    // Navigation actions
    navigateToChat,
    navigateToAgent,
    navigateToDocument,
    createNewChat,
    uploadDocument,
    openVoiceChat,
    switchModel,
    openGlobalSearch,

    // State helpers
    isCurrentPath,
    isCurrentArea,
    getBreadcrumbs,
    getCurrentSection,

    // Current state - simplified
    currentPath: pathname,
    currentArea: getCurrentArea(),
    hasActiveSession: pathname.startsWith('/conversations'),
    isInKnowledgeArea: pathname.startsWith('/knowledge')
  };
}

// Hook for quick actions with navigation helpers
export function useQuickActionsWithNavigation() {
  const helpers = useNavigationHelpers();

  const quickActionHandlers = {
    'new-chat': helpers.createNewChat,
    'upload-document': helpers.uploadDocument,
    'voice-mode': helpers.openVoiceChat,
    'switch-model': helpers.switchModel,
    'global-search': helpers.openGlobalSearch,
    'save-session': () => {
      // TODO: Implement session saving
      console.log('Saving current session...');
    },
    'pin-chat': () => {
      // TODO: Implement chat pinning
      console.log('Pinning current chat...');
    }
  };

  return {
    ...helpers,
    quickActionHandlers
  };
}
