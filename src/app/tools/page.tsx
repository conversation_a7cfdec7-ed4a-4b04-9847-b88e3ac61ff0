import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faCog, 
  faRobot, 
  faKey, 
  faLink, 
  faMicrophone,
  faGear,
  faChartBar,
  faServer,
  faShield
} from '@fortawesome/free-solid-svg-icons';

export default function ToolsPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          <FontAwesomeIcon icon={faCog} className="mr-3" />
          Tools & Settings
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Configure your AI models, integrations, and system preferences
        </p>
      </div>

      {/* Settings Categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* AI Models */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <FontAwesomeIcon icon={faRobot} className="h-8 w-8 text-blue-500" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white ml-3">
              AI Models
            </h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Configure and manage your AI model providers and settings
          </p>
          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Active Models:</span>
              <span className="text-gray-900 dark:text-white">3</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Default:</span>
              <span className="text-gray-900 dark:text-white">GPT-4</span>
            </div>
          </div>
          <button className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
            Manage Models
          </button>
        </div>

        {/* API Keys */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <FontAwesomeIcon icon={faKey} className="h-8 w-8 text-green-500" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white ml-3">
              API Keys
            </h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Manage your API keys for various AI providers and services
          </p>
          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Configured:</span>
              <span className="text-gray-900 dark:text-white">5</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Status:</span>
              <span className="text-green-600 dark:text-green-400">All Active</span>
            </div>
          </div>
          <button className="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
            Manage Keys
          </button>
        </div>

        {/* Integrations */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <FontAwesomeIcon icon={faLink} className="h-8 w-8 text-purple-500" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white ml-3">
              Integrations
            </h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Connect with external services and platforms
          </p>
          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Connected:</span>
              <span className="text-gray-900 dark:text-white">8</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Available:</span>
              <span className="text-gray-900 dark:text-white">15</span>
            </div>
          </div>
          <button className="w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors">
            View Integrations
          </button>
        </div>

        {/* Voice & Audio */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <FontAwesomeIcon icon={faMicrophone} className="h-8 w-8 text-orange-500" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white ml-3">
              Voice & Audio
            </h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Configure speech-to-text and text-to-speech settings
          </p>
          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Voice Input:</span>
              <span className="text-green-600 dark:text-green-400">Enabled</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Voice Output:</span>
              <span className="text-green-600 dark:text-green-400">Enabled</span>
            </div>
          </div>
          <button className="w-full bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700 transition-colors">
            Audio Settings
          </button>
        </div>

        {/* Preferences */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <FontAwesomeIcon icon={faGear} className="h-8 w-8 text-gray-500" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white ml-3">
              Preferences
            </h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Customize your user interface and experience settings
          </p>
          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Theme:</span>
              <span className="text-gray-900 dark:text-white">Auto</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Language:</span>
              <span className="text-gray-900 dark:text-white">English</span>
            </div>
          </div>
          <button className="w-full bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
            Edit Preferences
          </button>
        </div>

        {/* Usage Analytics */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <FontAwesomeIcon icon={faChartBar} className="h-8 w-8 text-indigo-500" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white ml-3">
              Usage Analytics
            </h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            View your usage statistics and performance metrics
          </p>
          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">This Month:</span>
              <span className="text-gray-900 dark:text-white">1,247 queries</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Avg Response:</span>
              <span className="text-gray-900 dark:text-white">1.2s</span>
            </div>
          </div>
          <button className="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors">
            View Analytics
          </button>
        </div>
      </div>

      {/* System Status */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
            <FontAwesomeIcon icon={faServer} className="mr-3" />
            System Status
          </h2>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-2">
                <FontAwesomeIcon icon={faShield} className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">Security</h3>
              <p className="text-sm text-green-600 dark:text-green-400">All systems secure</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-2">
                <FontAwesomeIcon icon={faServer} className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">Performance</h3>
              <p className="text-sm text-blue-600 dark:text-blue-400">Optimal</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-2">
                <FontAwesomeIcon icon={faLink} className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-white">Connectivity</h3>
              <p className="text-sm text-purple-600 dark:text-purple-400">All services online</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
