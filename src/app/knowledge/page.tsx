import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faBook, 
  faFile, 
  faUpload, 
  faSearch, 
  faGraduationCap,
  faChartBar,
  faFolder
} from '@fortawesome/free-solid-svg-icons';

export default function KnowledgePage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          <FontAwesomeIcon icon={faBook} className="mr-3" />
          Knowledge Base
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Manage your documents, learning resources, and knowledge repository
        </p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faFile} className="h-8 w-8 text-blue-500" />
            <div className="ml-4">
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">24</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Documents</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faFolder} className="h-8 w-8 text-green-500" />
            <div className="ml-4">
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">8</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Folders</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faGraduationCap} className="h-8 w-8 text-purple-500" />
            <div className="ml-4">
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">156</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Learning Items</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faChartBar} className="h-8 w-8 text-orange-500" />
            <div className="ml-4">
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">89%</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Processed</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="text-center">
            <FontAwesomeIcon icon={faUpload} className="h-12 w-12 text-blue-500 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Upload Documents
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Add new documents to your knowledge base
            </p>
            <button className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
              Upload Files
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="text-center">
            <FontAwesomeIcon icon={faSearch} className="h-12 w-12 text-green-500 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Search Knowledge
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Find information across all your documents
            </p>
            <button className="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
              Start Search
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="text-center">
            <FontAwesomeIcon icon={faGraduationCap} className="h-12 w-12 text-purple-500 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Learning Hub
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Access tutorials, articles, and resources
            </p>
            <button className="w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors">
              Explore Learning
            </button>
          </div>
        </div>
      </div>

      {/* Recent Documents */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Recent Documents
          </h2>
        </div>
        
        <div className="p-6">
          <div className="space-y-4">
            {/* Placeholder document items */}
            {[
              { name: 'AI Research Paper.pdf', type: 'PDF', size: '2.4 MB', status: 'Processed' },
              { name: 'Meeting Notes.docx', type: 'Word', size: '156 KB', status: 'Processing' },
              { name: 'Project Proposal.txt', type: 'Text', size: '45 KB', status: 'Processed' }
            ].map((doc, i) => (
              <div key={i} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <FontAwesomeIcon icon={faFile} className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">
                      {doc.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {doc.type} • {doc.size}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    doc.status === 'Processed' 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  }`}>
                    {doc.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
