@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  overflow: hidden; /* Prevent body scrollbar */
}

/* Custom scrollbar styling */
/* For Webkit browsers (Chrome, Safari) */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* For Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.05);
}

/* Dark mode scrollbar */
@media (prefers-color-scheme: dark) {
  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  * {
    scrollbar-color: rgba(255, 255, 255, 0.2) rgba(255, 255, 255, 0.05);
  }
}

/* Sidebar toggle for all screen sizes */
aside {
  transition: width 0.3s ease-in-out, transform 0.3s ease-in-out;

}

/* Prevent text from wrapping during sidebar transition */
aside .text-container {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Ensure icons are always visible */
aside .icon-container {
  opacity: 1 !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Handle text visibility during transition */
aside.w-64 .text-container {
  opacity: 1;
  transition: opacity 0.1s ease-in-out 0.2s; /* Delay showing text until sidebar is mostly expanded */
}

aside.w-16 .text-container {
  opacity: 0;
  transition: opacity 0.1s ease-in-out; /* Hide text immediately when collapsing */
}

/* Responsive sidebar and toggle behavior */
@media (max-width: 767px) {
  /* Hide sidebar toggle on mobile/tablet */
  aside button[aria-label="Toggle sidebar"] {
    display: none;
  }
}

@media (min-width: 768px) {
  /* Hide header toggle on desktop */
  header button[aria-label="Toggle menu"] {
    display: none;
  }
}

/* Fixed header with frosted glass effect */
header {
  height: 64px;
  background-color: rgba(255, 255, 255, 0.8) !important; /* Semi-transparent background */
  backdrop-filter: blur(8px) !important; /* Frosted glass effect */
  -webkit-backdrop-filter: blur(8px) !important; /* For Safari */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
}

/* Dark mode header */
@media (prefers-color-scheme: dark) {
  header {
    background-color: rgba(17, 24, 39, 0.8) !important; /* Semi-transparent dark background */
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  }
}

/* Ensure sidebar is positioned below header and spans full height with frosted glass effect */
aside {
  top: 64px !important; /* Match header height */
  bottom: 0 !important; /* Extend to bottom of screen */
  height: calc(100vh - 64px) !important; /* Full viewport height minus header */
  background-color: rgba(255, 255, 255, 0.8) !important; /* Semi-transparent background */
  backdrop-filter: blur(8px) !important; /* Frosted glass effect */
  -webkit-backdrop-filter: blur(8px) !important; /* For Safari */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important; /* Add shadow for floating effect */
  border-right: 1px solid rgba(0, 0, 0, 0.1) !important;
}

/* Dark mode sidebar */
@media (prefers-color-scheme: dark) {
  aside {
    background-color: rgba(17, 24, 39, 0.8) !important; /* Semi-transparent dark background */
    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
  }
}

/* Ensure main content starts below header */
.flex.flex-1.pt-16 {
  padding-top: 64px !important; /* Exact header height */
  margin-top: 0 !important;
}

main {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* Mobile sidebar toggle */
@media (max-width: 768px) {
  aside {
    transform: translateX(-100%);
  }

  html.sidebar-open aside {
    transform: translateX(0);
  }

  main {
    padding-left: 0 !important; /* No padding on mobile */
  }

  /* Add overlay when sidebar is open on mobile */
  html.sidebar-open {
    overflow: hidden;
  }

  html.sidebar-open::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 40; /* Below sidebar (z-50) but above content */
  }
}

/* Sidebar overlay behavior */
@media (min-width: 769px) {
  /* Main content always has padding equal to collapsed sidebar width */
  main {
    padding-left: 4rem !important; /* 4rem = 64px, width of collapsed sidebar */
  }

  /* Expanded sidebar overlays the padding area with higher z-index */
}

/* Disable text selection during panning */
.no-select {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Enhanced Navigation Styles */
@import '../styles/navigation.css';
