import Link from "next/link";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faArrowRight,
  faGraduationCap,
  faVideo,
  faNewspaper,
  faBlog,
  faToolbox,
  faFlask,
  faIndustry,
  faLandmark,
  faCalendarAlt
} from "@fortawesome/free-solid-svg-icons";
import { getAllAgents, getFeaturedAgents, getNewAgents } from "@/lib/agents";
import { getCombinedRecentContent, getCombinedPopularContent } from "@/data/combined-resources";
import { getFeaturedNews, getLatestNews } from "@/data/news-data";
// import AnimatedBackgroundWrapper from "@/components/animations/AnimatedBackgroundWrapper";

export default function Home() {
  // Get agents from our data utility
  const featuredAgents = getFeaturedAgents();
  const allAgents = getAllAgents();

  // For recently used, we'll simulate by taking the first 2 agents
  // In a real app, this would come from user history
  const recentAgents = allAgents.slice(0, 2);

  // Get new agents
  const newAgents = getNewAgents();

  // Get learning content
  const recentLearningContent = getCombinedRecentContent(4);
  const popularLearningContent = getCombinedPopularContent(4);

  // Get news content
  const featuredNews = getFeaturedNews(2);
  const latestNews = getLatestNews(4);

  return (
    <div className="space-y-10">
      {/* Futuristic animated background - temporarily disabled */}
      {/* <AnimatedBackgroundWrapper /> */}

      {/* Colorful elements to enhance the frosted glass effect */}
      <div className="absolute top-0 left-0 right-0 -z-10 overflow-hidden">
        <div className="h-64 w-64 rounded-full bg-blue-500 opacity-10 blur-3xl absolute -top-20 -left-20"></div>
        <div className="h-64 w-64 rounded-full bg-purple-500 opacity-10 blur-3xl absolute top-10 left-40"></div>
        <div className="h-64 w-64 rounded-full bg-pink-500 opacity-10 blur-3xl absolute -top-10 right-20"></div>
      </div>
      {/* Welcome section */}
      <section className="rounded-lg bg-gradient-to-r from-blue-600 to-indigo-700 p-8 text-white">
        <div className="mx-auto max-w-4xl">
          <h1 className="mb-4 text-3xl font-bold md:text-4xl">Welcome to our AI Hub</h1>
          <p className="mb-6 text-lg md:text-xl">
            Discover and interact with our collection of AI agents designed to help you work more efficiently & Learn about AI tools and services in our learning hub.
          </p>
          <div className="flex flex-wrap gap-4">
            <Link
              href="/browse"
              className="rounded-md bg-white px-4 py-2 font-medium text-blue-700 hover:bg-blue-50"
            >
              Browse All Agents
            </Link>
            <Link
              href="/favorites"
              className="rounded-md bg-blue-800 px-4 py-2 font-medium text-white hover:bg-blue-900"
            >
              View Favorites
            </Link>
          </div>
        </div>
      </section>

      {/* Featured agents section */}
      <section>
        <div className="mb-6 flex items-center justify-between">
          <h2 className="text-2xl font-bold">Featured Agents</h2>
          <Link href="/browse" className="text-blue-600 hover:underline dark:text-blue-400">
            View all
          </Link>
        </div>
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {featuredAgents.map((agent) => (
            <div
              key={agent.id}
              className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900"
            >
              <div className="mb-4 flex items-center justify-between">
                <span className="rounded-full bg-gray-100 px-3 py-1 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-200">
                  {agent.category}
                </span>
                {agent.isNew && (
                  <span className="rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400">
                    New
                  </span>
                )}
              </div>
              <h3 className="mb-2 text-xl font-bold">{agent.name}</h3>
              <p className="mb-4 text-sm text-gray-600 dark:text-gray-400">{agent.description}</p>
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500 dark:text-gray-500">
                  {agent.usageCount.toLocaleString()} uses
                </span>
                <Link
                  href={`/agent/${agent.id}`}
                  className="rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
                >
                  View Agent
                </Link>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Recent and New sections in a grid */}
      <div className="grid grid-cols-1 gap-10 lg:grid-cols-2">
        {/* Recently used section */}
        <section>
          <div className="mb-6 flex items-center justify-between">
            <h2 className="text-2xl font-bold">Recently Used</h2>
            <Link href="/recent" className="text-blue-600 hover:underline dark:text-blue-400">
              View all
            </Link>
          </div>
          <div className="space-y-4">
            {recentAgents.map((agent) => (
              <div
                key={agent.id}
                className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-bold">{agent.name}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{agent.description}</p>
                  </div>
                  <Link
                    href={`/agent/${agent.id}`}
                    className="rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
                  >
                    View Agent
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* New agents section */}
        <section>
          <div className="mb-6 flex items-center justify-between">
            <h2 className="text-2xl font-bold">New Agents</h2>
            <Link href="/new-releases" className="text-blue-600 hover:underline dark:text-blue-400">
              View all
            </Link>
          </div>
          <div className="space-y-4">
            {newAgents.map((agent) => (
              <div
                key={agent.id}
                className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="mb-1 flex items-center gap-2">
                      <h3 className="font-bold">{agent.name}</h3>
                      <span className="rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400">
                        New
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{agent.description}</p>
                  </div>
                  <Link
                    href={`/agent/${agent.id}`}
                    className="rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
                  >
                    View Agent
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </section>

      </div>

      {/* Learning section - Full width */}
      <section className="mt-12 rounded-lg bg-gradient-to-r from-indigo-100 to-purple-100 p-6 dark:from-indigo-950/30 dark:to-purple-950/30 w-full">
        <div className="mx-auto max-w-7xl">
          <div className="mb-6 flex items-center">
            <FontAwesomeIcon icon={faGraduationCap} className="mr-3 h-6 w-6 text-indigo-600 dark:text-indigo-400" />
            <h2 className="text-2xl font-bold">Learning Hub</h2>
          </div>

          {/* Recent learning content */}
          <div className="mb-8">
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-lg font-semibold">Recent Content</h3>
              <Link href="/learning" className="flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">
                Explore all
                <FontAwesomeIcon icon={faArrowRight} className="ml-1 h-3 w-3" />
              </Link>
            </div>
            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
              {recentLearningContent.map((item) => (
                <Link
                  key={item.id}
                  href={item.url}
                  className="group overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900"
                >
                  <div className="relative h-36 overflow-hidden">
                    <img
                      src={item.thumbnail || `https://via.placeholder.com/400x200?text=${encodeURIComponent(item.title)}`}
                      alt={item.title}
                      className="h-full w-full object-cover transition duration-300 group-hover:scale-105"
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
                      <div className="flex items-center">
                        <span className="rounded bg-white/20 px-2 py-1 text-xs font-medium text-white backdrop-blur-sm">
                          {item.type === 'video' && (
                            <>
                              <FontAwesomeIcon icon={faVideo} className="mr-1 h-3 w-3" />
                              Video
                            </>
                          )}
                          {item.type === 'article' && (
                            <>
                              <FontAwesomeIcon icon={faNewspaper} className="mr-1 h-3 w-3" />
                              Article
                            </>
                          )}
                          {item.type === 'blog' && (
                            <>
                              <FontAwesomeIcon icon={faBlog} className="mr-1 h-3 w-3" />
                              Blog
                            </>
                          )}
                          {item.type === 'resource' && (
                            <>
                              <FontAwesomeIcon icon={faToolbox} className="mr-1 h-3 w-3" />
                              Resource
                            </>
                          )}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="p-4">
                    <h4 className="mb-1 font-medium text-gray-900 line-clamp-1 group-hover:text-indigo-600 dark:text-white dark:group-hover:text-indigo-400">
                      {item.title}
                    </h4>
                    <p className="text-sm text-gray-600 line-clamp-2 dark:text-gray-400">
                      {item.description}
                    </p>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Popular learning content */}
          <div>
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-lg font-semibold">Popular Resources</h3>
              <Link href="/learning/resources" className="flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">
                View all resources
                <FontAwesomeIcon icon={faArrowRight} className="ml-1 h-3 w-3" />
              </Link>
            </div>
            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
              {popularLearningContent.map((item) => (
                <Link
                  key={item.id}
                  href={item.url}
                  className="group overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900"
                >
                  <div className="p-4">
                    <div className="mb-3 flex items-center">
                      {item.type === 'video' && (
                        <FontAwesomeIcon icon={faVideo} className="mr-2 h-4 w-4 text-blue-500" />
                      )}
                      {item.type === 'article' && (
                        <FontAwesomeIcon icon={faNewspaper} className="mr-2 h-4 w-4 text-green-500" />
                      )}
                      {item.type === 'blog' && (
                        <FontAwesomeIcon icon={faBlog} className="mr-2 h-4 w-4 text-purple-500" />
                      )}
                      {item.type === 'resource' && (
                        <FontAwesomeIcon icon={faToolbox} className="mr-2 h-4 w-4 text-orange-500" />
                      )}
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                      </span>
                    </div>
                    <h4 className="mb-1 font-medium text-gray-900 line-clamp-1 group-hover:text-indigo-600 dark:text-white dark:group-hover:text-indigo-400">
                      {item.title}
                    </h4>
                    <p className="text-sm text-gray-600 line-clamp-2 dark:text-gray-400">
                      {item.description}
                    </p>
                    {item.tags && item.tags.length > 0 && (
                      <div className="mt-3 flex flex-wrap gap-1">
                        {item.tags.slice(0, 3).map((tag) => (
                          <span
                            key={tag}
                            className="inline-flex items-center rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-200"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* AI News Section */}
      <section className="py-10 bg-gray-50 dark:bg-gray-900/50">
        <div className="container mx-auto px-4">
          <div className="mb-8 text-center">
            <h2 className="text-3xl font-bold mb-2">AI News & Updates</h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Stay informed about the latest advancements and applications in artificial intelligence
            </p>
          </div>

          {/* Featured news */}
          <div className="mb-12">
            <div className="mb-6 flex items-center justify-between">
              <h3 className="text-xl font-semibold">Featured Stories</h3>
              <Link href="/news" className="flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">
                View all news
                <FontAwesomeIcon icon={faArrowRight} className="ml-1 h-3 w-3" />
              </Link>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
              {featuredNews.map((item) => (
                <Link
                  key={item.id}
                  href={item.url}
                  className="group relative overflow-hidden rounded-xl bg-white shadow-lg transition hover:shadow-xl dark:bg-gray-800"
                >
                  <div className="aspect-w-16 aspect-h-9 relative overflow-hidden">
                    <img
                      src={item.imageUrl || `https://via.placeholder.com/800x450?text=${encodeURIComponent(item.title)}`}
                      alt={item.title}
                      className="h-full w-full object-cover transition duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent">
                      <div className="absolute bottom-0 left-0 right-0 p-6">
                        <div className="mb-2 flex items-center">
                          {item.category === 'research' && (
                            <span className="rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800 dark:bg-blue-900/60 dark:text-blue-200">
                              <FontAwesomeIcon icon={faFlask} className="mr-1 h-3 w-3" />
                              Research
                            </span>
                          )}
                          {item.category === 'industry' && (
                            <span className="rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900/60 dark:text-green-200">
                              <FontAwesomeIcon icon={faIndustry} className="mr-1 h-3 w-3" />
                              Industry
                            </span>
                          )}
                          {item.category === 'policy' && (
                            <span className="rounded-full bg-purple-100 px-3 py-1 text-xs font-medium text-purple-800 dark:bg-purple-900/60 dark:text-purple-200">
                              <FontAwesomeIcon icon={faLandmark} className="mr-1 h-3 w-3" />
                              Policy
                            </span>
                          )}
                          {item.category === 'events' && (
                            <span className="rounded-full bg-amber-100 px-3 py-1 text-xs font-medium text-amber-800 dark:bg-amber-900/60 dark:text-amber-200">
                              <FontAwesomeIcon icon={faCalendarAlt} className="mr-1 h-3 w-3" />
                              Events
                            </span>
                          )}
                          <span className="ml-auto text-xs text-white opacity-80">{item.date}</span>
                        </div>
                        <h4 className="mb-2 text-xl font-bold text-white">{item.title}</h4>
                        <p className="mb-2 text-sm text-white/80">{item.description}</p>
                        <div className="flex items-center text-xs text-white/70">
                          <span>Source: {item.source}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Latest news */}
          <div>
            <div className="mb-6 flex items-center justify-between">
              <h3 className="text-xl font-semibold">Latest Updates</h3>
              <Link href="/news" className="flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">
                View all news
                <FontAwesomeIcon icon={faArrowRight} className="ml-1 h-3 w-3" />
              </Link>
            </div>

            <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
              {latestNews.map((item) => (
                <Link
                  key={item.id}
                  href={item.url}
                  className="group overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition hover:shadow-md dark:border-gray-700 dark:bg-gray-800"
                >
                  <div className="relative h-40 overflow-hidden">
                    <img
                      src={item.imageUrl || `https://via.placeholder.com/400x200?text=${encodeURIComponent(item.title)}`}
                      alt={item.title}
                      className="h-full w-full object-cover transition duration-300 group-hover:scale-105"
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
                      <div className="flex items-center">
                        {item.category === 'research' && (
                          <span className="rounded bg-blue-500/20 px-2 py-1 text-xs font-medium text-white backdrop-blur-sm">
                            <FontAwesomeIcon icon={faFlask} className="mr-1 h-3 w-3" />
                            Research
                          </span>
                        )}
                        {item.category === 'industry' && (
                          <span className="rounded bg-green-500/20 px-2 py-1 text-xs font-medium text-white backdrop-blur-sm">
                            <FontAwesomeIcon icon={faIndustry} className="mr-1 h-3 w-3" />
                            Industry
                          </span>
                        )}
                        {item.category === 'policy' && (
                          <span className="rounded bg-purple-500/20 px-2 py-1 text-xs font-medium text-white backdrop-blur-sm">
                            <FontAwesomeIcon icon={faLandmark} className="mr-1 h-3 w-3" />
                            Policy
                          </span>
                        )}
                        {item.category === 'events' && (
                          <span className="rounded bg-amber-500/20 px-2 py-1 text-xs font-medium text-white backdrop-blur-sm">
                            <FontAwesomeIcon icon={faCalendarAlt} className="mr-1 h-3 w-3" />
                            Events
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="p-4">
                    <div className="mb-1 flex items-center justify-between">
                      <span className="text-xs text-gray-500 dark:text-gray-400">{item.date}</span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">{item.source}</span>
                    </div>
                    <h4 className="mb-1 font-medium text-gray-900 line-clamp-2 group-hover:text-indigo-600 dark:text-white dark:group-hover:text-indigo-400">
                      {item.title}
                    </h4>
                    <p className="text-sm text-gray-600 line-clamp-2 dark:text-gray-400">
                      {item.description}
                    </p>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
