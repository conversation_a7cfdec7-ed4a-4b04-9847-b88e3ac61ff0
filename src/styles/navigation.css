/* Enhanced Navigation Styles */

:root {
  --sidebar-width: 320px;
  --sidebar-collapsed-width: 64px;
  --sidebar-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Sidebar responsive behavior */
.sidebar-collapsed {
  --sidebar-width: 64px;
}

/* Mobile sidebar overlay */
@media (max-width: 768px) {
  .sidebar-open::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 40;
    backdrop-filter: blur(2px);
  }
  
  .sidebar-open {
    overflow: hidden;
  }
}

/* Smooth animations for sidebar sections */
.sidebar-section {
  transition: var(--sidebar-transition);
}

.sidebar-section.collapsed {
  max-height: 0;
  overflow: hidden;
  opacity: 0;
}

.sidebar-section.expanded {
  max-height: 1000px;
  opacity: 1;
}

/* Badge animations */
.sidebar-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Quick actions animations */
.quick-actions-enter {
  animation: slideInUp 0.3s ease-out;
}

.quick-actions-exit {
  animation: slideOutDown 0.3s ease-in;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

/* Hover effects for navigation items */
.nav-item {
  position: relative;
  transition: var(--sidebar-transition);
}

.nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: transparent;
  transition: var(--sidebar-transition);
}

.nav-item.active::before {
  background: #3b82f6;
}

.nav-item:hover::before {
  background: #93c5fd;
}

/* Search input focus styles */
.sidebar-search:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Responsive adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  :root {
    --sidebar-width: 280px;
  }
}

@media (min-width: 1025px) {
  :root {
    --sidebar-width: 320px;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .nav-item.active::before {
    background: #60a5fa;
  }
  
  .nav-item:hover::before {
    background: #3b82f6;
  }
}

/* Accessibility improvements */
.nav-item:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.quick-action:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading states */
.nav-loading {
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.nav-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
}

/* Tooltip styles for collapsed sidebar */
.sidebar-tooltip {
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 8px;
  padding: 8px 12px;
  background: #1f2937;
  color: white;
  border-radius: 6px;
  font-size: 14px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  z-index: 1000;
}

.sidebar-tooltip::before {
  content: '';
  position: absolute;
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border: 4px solid transparent;
  border-right-color: #1f2937;
}

.nav-item:hover .sidebar-tooltip {
  opacity: 1;
}

/* Print styles */
@media print {
  .enhanced-sidebar,
  .quick-actions {
    display: none !important;
  }
  
  main {
    margin-left: 0 !important;
  }
}
