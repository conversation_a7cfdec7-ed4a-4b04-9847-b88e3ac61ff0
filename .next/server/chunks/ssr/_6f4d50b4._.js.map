{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/tools/page.tsx"], "sourcesContent": ["import React from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { \n  faCog, \n  faRobot, \n  faKey, \n  faLink, \n  faMicrophone,\n  faGear,\n  faChartBar,\n  faServer,\n  faShield\n} from '@fortawesome/free-solid-svg-icons';\n\nexport default function ToolsPage() {\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"border-b border-gray-200 pb-4 dark:border-gray-700\">\n        <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n          <FontAwesomeIcon icon={faCog} className=\"mr-3\" />\n          Tools & Settings\n        </h1>\n        <p className=\"mt-2 text-gray-600 dark:text-gray-400\">\n          Configure your AI models, integrations, and system preferences\n        </p>\n      </div>\n\n      {/* Settings Categories */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {/* AI Models */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow\">\n          <div className=\"flex items-center mb-4\">\n            <FontAwesomeIcon icon={faRobot} className=\"h-8 w-8 text-blue-500\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white ml-3\">\n              AI Models\n            </h3>\n          </div>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n            Configure and manage your AI model providers and settings\n          </p>\n          <div className=\"space-y-2 mb-4\">\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-gray-600 dark:text-gray-400\">Active Models:</span>\n              <span className=\"text-gray-900 dark:text-white\">3</span>\n            </div>\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-gray-600 dark:text-gray-400\">Default:</span>\n              <span className=\"text-gray-900 dark:text-white\">GPT-4</span>\n            </div>\n          </div>\n          <button className=\"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\">\n            Manage Models\n          </button>\n        </div>\n\n        {/* API Keys */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow\">\n          <div className=\"flex items-center mb-4\">\n            <FontAwesomeIcon icon={faKey} className=\"h-8 w-8 text-green-500\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white ml-3\">\n              API Keys\n            </h3>\n          </div>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n            Manage your API keys for various AI providers and services\n          </p>\n          <div className=\"space-y-2 mb-4\">\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-gray-600 dark:text-gray-400\">Configured:</span>\n              <span className=\"text-gray-900 dark:text-white\">5</span>\n            </div>\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-gray-600 dark:text-gray-400\">Status:</span>\n              <span className=\"text-green-600 dark:text-green-400\">All Active</span>\n            </div>\n          </div>\n          <button className=\"w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors\">\n            Manage Keys\n          </button>\n        </div>\n\n        {/* Integrations */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow\">\n          <div className=\"flex items-center mb-4\">\n            <FontAwesomeIcon icon={faLink} className=\"h-8 w-8 text-purple-500\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white ml-3\">\n              Integrations\n            </h3>\n          </div>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n            Connect with external services and platforms\n          </p>\n          <div className=\"space-y-2 mb-4\">\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-gray-600 dark:text-gray-400\">Connected:</span>\n              <span className=\"text-gray-900 dark:text-white\">8</span>\n            </div>\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-gray-600 dark:text-gray-400\">Available:</span>\n              <span className=\"text-gray-900 dark:text-white\">15</span>\n            </div>\n          </div>\n          <button className=\"w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors\">\n            View Integrations\n          </button>\n        </div>\n\n        {/* Voice & Audio */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow\">\n          <div className=\"flex items-center mb-4\">\n            <FontAwesomeIcon icon={faMicrophone} className=\"h-8 w-8 text-orange-500\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white ml-3\">\n              Voice & Audio\n            </h3>\n          </div>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n            Configure speech-to-text and text-to-speech settings\n          </p>\n          <div className=\"space-y-2 mb-4\">\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-gray-600 dark:text-gray-400\">Voice Input:</span>\n              <span className=\"text-green-600 dark:text-green-400\">Enabled</span>\n            </div>\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-gray-600 dark:text-gray-400\">Voice Output:</span>\n              <span className=\"text-green-600 dark:text-green-400\">Enabled</span>\n            </div>\n          </div>\n          <button className=\"w-full bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700 transition-colors\">\n            Audio Settings\n          </button>\n        </div>\n\n        {/* Preferences */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow\">\n          <div className=\"flex items-center mb-4\">\n            <FontAwesomeIcon icon={faGear} className=\"h-8 w-8 text-gray-500\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white ml-3\">\n              Preferences\n            </h3>\n          </div>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n            Customize your user interface and experience settings\n          </p>\n          <div className=\"space-y-2 mb-4\">\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-gray-600 dark:text-gray-400\">Theme:</span>\n              <span className=\"text-gray-900 dark:text-white\">Auto</span>\n            </div>\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-gray-600 dark:text-gray-400\">Language:</span>\n              <span className=\"text-gray-900 dark:text-white\">English</span>\n            </div>\n          </div>\n          <button className=\"w-full bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors\">\n            Edit Preferences\n          </button>\n        </div>\n\n        {/* Usage Analytics */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow\">\n          <div className=\"flex items-center mb-4\">\n            <FontAwesomeIcon icon={faChartBar} className=\"h-8 w-8 text-indigo-500\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white ml-3\">\n              Usage Analytics\n            </h3>\n          </div>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n            View your usage statistics and performance metrics\n          </p>\n          <div className=\"space-y-2 mb-4\">\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-gray-600 dark:text-gray-400\">This Month:</span>\n              <span className=\"text-gray-900 dark:text-white\">1,247 queries</span>\n            </div>\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-gray-600 dark:text-gray-400\">Avg Response:</span>\n              <span className=\"text-gray-900 dark:text-white\">1.2s</span>\n            </div>\n          </div>\n          <button className=\"w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors\">\n            View Analytics\n          </button>\n        </div>\n      </div>\n\n      {/* System Status */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\n        <div className=\"p-6 border-b border-gray-200 dark:border-gray-700\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white flex items-center\">\n            <FontAwesomeIcon icon={faServer} className=\"mr-3\" />\n            System Status\n          </h2>\n        </div>\n        \n        <div className=\"p-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-2\">\n                <FontAwesomeIcon icon={faShield} className=\"h-6 w-6 text-green-600 dark:text-green-400\" />\n              </div>\n              <h3 className=\"font-medium text-gray-900 dark:text-white\">Security</h3>\n              <p className=\"text-sm text-green-600 dark:text-green-400\">All systems secure</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-2\">\n                <FontAwesomeIcon icon={faServer} className=\"h-6 w-6 text-blue-600 dark:text-blue-400\" />\n              </div>\n              <h3 className=\"font-medium text-gray-900 dark:text-white\">Performance</h3>\n              <p className=\"text-sm text-blue-600 dark:text-blue-400\">Optimal</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-2\">\n                <FontAwesomeIcon icon={faLink} className=\"h-6 w-6 text-purple-600 dark:text-purple-400\" />\n              </div>\n              <h3 className=\"font-medium text-gray-900 dark:text-white\">Connectivity</h3>\n              <p className=\"text-sm text-purple-600 dark:text-purple-400\">All services online</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAYe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,oKAAA,CAAA,kBAAe;gCAAC,MAAM,wKAAA,CAAA,QAAK;gCAAE,WAAU;;;;;;4BAAS;;;;;;;kCAGnD,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;;;;;;;0BAMvD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,UAAO;wCAAE,WAAU;;;;;;kDAC1C,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;;;;;;;0CAI3E,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;0CAG7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAElD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAGpD,8OAAC;gCAAO,WAAU;0CAAyF;;;;;;;;;;;;kCAM7G,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,QAAK;wCAAE,WAAU;;;;;;kDACxC,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;;;;;;;0CAI3E,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;0CAG7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAElD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,8OAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;0CAGzD,8OAAC;gCAAO,WAAU;0CAA2F;;;;;;;;;;;;kCAM/G,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,SAAM;wCAAE,WAAU;;;;;;kDACzC,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;;;;;;;0CAI3E,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;0CAG7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAElD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAGpD,8OAAC;gCAAO,WAAU;0CAA6F;;;;;;;;;;;;kCAMjH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,eAAY;wCAAE,WAAU;;;;;;kDAC/C,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;;;;;;;0CAI3E,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;0CAG7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,8OAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;;kDAEvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,8OAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;0CAGzD,8OAAC;gCAAO,WAAU;0CAA6F;;;;;;;;;;;;kCAMjH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,SAAM;wCAAE,WAAU;;;;;;kDACzC,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;;;;;;;0CAI3E,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;0CAG7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAElD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAGpD,8OAAC;gCAAO,WAAU;0CAAyF;;;;;;;;;;;;kCAM7G,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,aAAU;wCAAE,WAAU;;;;;;kDAC7C,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;;;;;;;0CAI3E,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;0CAG7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAElD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAGpD,8OAAC;gCAAO,WAAU;0CAA6F;;;;;;;;;;;;;;;;;;0BAOnH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,WAAQ;oCAAE,WAAU;;;;;;gCAAS;;;;;;;;;;;;kCAKxD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;gDAAC,MAAM,wKAAA,CAAA,WAAQ;gDAAE,WAAU;;;;;;;;;;;sDAE7C,8OAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,8OAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;8CAG5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;gDAAC,MAAM,wKAAA,CAAA,WAAQ;gDAAE,WAAU;;;;;;;;;;;sDAE7C,8OAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;8CAG1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;gDAAC,MAAM,wKAAA,CAAA,SAAM;gDAAE,WAAU;;;;;;;;;;;sDAE3C,8OAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1E", "debugId": null}}]}