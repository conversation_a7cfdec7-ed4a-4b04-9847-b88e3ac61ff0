{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/knowledge/page.tsx"], "sourcesContent": ["import React from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { \n  faBook, \n  faFile, \n  faUpload, \n  faSearch, \n  faGraduationCap,\n  faChartBar,\n  faFolder\n} from '@fortawesome/free-solid-svg-icons';\n\nexport default function KnowledgePage() {\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"border-b border-gray-200 pb-4 dark:border-gray-700\">\n        <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n          <FontAwesomeIcon icon={faBook} className=\"mr-3\" />\n          Knowledge Base\n        </h1>\n        <p className=\"mt-2 text-gray-600 dark:text-gray-400\">\n          Manage your documents, learning resources, and knowledge repository\n        </p>\n      </div>\n\n      {/* Stats Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n          <div className=\"flex items-center\">\n            <FontAwesomeIcon icon={faFile} className=\"h-8 w-8 text-blue-500\" />\n            <div className=\"ml-4\">\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-white\">24</p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Documents</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n          <div className=\"flex items-center\">\n            <FontAwesomeIcon icon={faFolder} className=\"h-8 w-8 text-green-500\" />\n            <div className=\"ml-4\">\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-white\">8</p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Folders</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n          <div className=\"flex items-center\">\n            <FontAwesomeIcon icon={faGraduationCap} className=\"h-8 w-8 text-purple-500\" />\n            <div className=\"ml-4\">\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-white\">156</p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Learning Items</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n          <div className=\"flex items-center\">\n            <FontAwesomeIcon icon={faChartBar} className=\"h-8 w-8 text-orange-500\" />\n            <div className=\"ml-4\">\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-white\">89%</p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Processed</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n          <div className=\"text-center\">\n            <FontAwesomeIcon icon={faUpload} className=\"h-12 w-12 text-blue-500 mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n              Upload Documents\n            </h3>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n              Add new documents to your knowledge base\n            </p>\n            <button className=\"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\">\n              Upload Files\n            </button>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n          <div className=\"text-center\">\n            <FontAwesomeIcon icon={faSearch} className=\"h-12 w-12 text-green-500 mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n              Search Knowledge\n            </h3>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n              Find information across all your documents\n            </p>\n            <button className=\"w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors\">\n              Start Search\n            </button>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n          <div className=\"text-center\">\n            <FontAwesomeIcon icon={faGraduationCap} className=\"h-12 w-12 text-purple-500 mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n              Learning Hub\n            </h3>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n              Access tutorials, articles, and resources\n            </p>\n            <button className=\"w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors\">\n              Explore Learning\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Recent Documents */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\n        <div className=\"p-6 border-b border-gray-200 dark:border-gray-700\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n            Recent Documents\n          </h2>\n        </div>\n        \n        <div className=\"p-6\">\n          <div className=\"space-y-4\">\n            {/* Placeholder document items */}\n            {[\n              { name: 'AI Research Paper.pdf', type: 'PDF', size: '2.4 MB', status: 'Processed' },\n              { name: 'Meeting Notes.docx', type: 'Word', size: '156 KB', status: 'Processing' },\n              { name: 'Project Proposal.txt', type: 'Text', size: '45 KB', status: 'Processed' }\n            ].map((doc, i) => (\n              <div key={i} className=\"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center\">\n                    <FontAwesomeIcon icon={faFile} className=\"h-5 w-5 text-blue-600 dark:text-blue-400\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-medium text-gray-900 dark:text-white\">\n                      {doc.name}\n                    </h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {doc.type} • {doc.size}\n                    </p>\n                  </div>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <span className={`text-xs px-2 py-1 rounded-full ${\n                    doc.status === 'Processed' \n                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n                  }`}>\n                    {doc.status}\n                  </span>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAUe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,oKAAA,CAAA,kBAAe;gCAAC,MAAM,wKAAA,CAAA,SAAM;gCAAE,WAAU;;;;;;4BAAS;;;;;;;kCAGpD,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;;;;;;;0BAMvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,SAAM;oCAAE,WAAU;;;;;;8CACzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;;;;;;kCAK9D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,WAAQ;oCAAE,WAAU;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;;;;;;kCAK9D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,kBAAe;oCAAE,WAAU;;;;;;8CAClD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;;;;;;kCAK9D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,aAAU;oCAAE,WAAU;;;;;;8CAC7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,WAAQ;oCAAE,WAAU;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAGzE,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,8OAAC;oCAAO,WAAU;8CAAyF;;;;;;;;;;;;;;;;;kCAM/G,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,WAAQ;oCAAE,WAAU;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAGzE,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,8OAAC;oCAAO,WAAU;8CAA2F;;;;;;;;;;;;;;;;;kCAMjH,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,kBAAe;oCAAE,WAAU;;;;;;8CAClD,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAGzE,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,8OAAC;oCAAO,WAAU;8CAA6F;;;;;;;;;;;;;;;;;;;;;;;0BAQrH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAsD;;;;;;;;;;;kCAKtE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAEZ;gCACC;oCAAE,MAAM;oCAAyB,MAAM;oCAAO,MAAM;oCAAU,QAAQ;gCAAY;gCAClF;oCAAE,MAAM;oCAAsB,MAAM;oCAAQ,MAAM;oCAAU,QAAQ;gCAAa;gCACjF;oCAAE,MAAM;oCAAwB,MAAM;oCAAQ,MAAM;oCAAS,QAAQ;gCAAY;6BAClF,CAAC,GAAG,CAAC,CAAC,KAAK,kBACV,8OAAC;oCAAY,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;wDAAC,MAAM,wKAAA,CAAA,SAAM;wDAAE,WAAU;;;;;;;;;;;8DAE3C,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,IAAI,IAAI;;;;;;sEAEX,8OAAC;4DAAE,WAAU;;gEACV,IAAI,IAAI;gEAAC;gEAAI,IAAI,IAAI;;;;;;;;;;;;;;;;;;;sDAI5B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAW,CAAC,+BAA+B,EAC/C,IAAI,MAAM,KAAK,cACX,sEACA,yEACJ;0DACC,IAAI,MAAM;;;;;;;;;;;;mCApBP;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BxB", "debugId": null}}]}