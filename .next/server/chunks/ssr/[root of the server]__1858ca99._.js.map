{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { User, AuthState, LoginCredentials, RegisterCredentials } from '@/types/auth';\n\ninterface AuthContextType extends AuthState {\n  login: (credentials: LoginCredentials) => Promise<void>;\n  register: (credentials: RegisterCredentials) => Promise<void>;\n  logout: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const router = useRouter();\n\n  const [authState, setAuthState] = useState<AuthState>({\n    user: null,\n    isLoading: true,\n    error: null,\n  });\n\n  // Load user from localStorage on mount\n  useEffect(() => {\n    const loadUser = () => {\n      try {\n        console.log('Loading user from localStorage...');\n        const userJson = localStorage.getItem('user');\n        console.log('User JSON from localStorage:', userJson);\n\n        if (userJson) {\n          const user = JSON.parse(userJson) as User;\n          console.log('Parsed user:', user);\n          setAuthState({\n            user,\n            isLoading: false,\n            error: null,\n          });\n        } else {\n          console.log('No user found in localStorage');\n          setAuthState({\n            user: null,\n            isLoading: false,\n            error: null,\n          });\n        }\n      } catch (error) {\n        console.error('Error loading user from localStorage:', error);\n        setAuthState({\n          user: null,\n          isLoading: false,\n          error: 'Failed to load user data',\n        });\n      }\n    };\n\n    // Only run in browser environment\n    if (typeof window !== 'undefined') {\n      loadUser();\n    }\n  }, []);\n\n  const login = async (credentials: LoginCredentials) => {\n    console.log('Login function called with:', credentials.email);\n    try {\n      console.log('Setting loading state...');\n      setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));\n\n      console.log('Sending login request to API...');\n      const response = await fetch('/api/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(credentials),\n      });\n\n      console.log('API response status:', response.status);\n      const data = await response.json();\n      console.log('API response data:', data);\n\n      if (!response.ok || !data.success) {\n        setAuthState((prev) => ({\n          ...prev,\n          isLoading: false,\n          error: data.message || 'Login failed',\n        }));\n        return;\n      }\n\n      console.log('Login successful, storing user data...');\n      // Store user in localStorage\n      localStorage.setItem('user', JSON.stringify(data.user));\n\n      // Also set a cookie for the middleware\n      document.cookie = `user=${JSON.stringify(data.user)}; path=/; max-age=86400`;\n\n      console.log('Updating auth state...');\n      // Update auth state\n      setAuthState({\n        user: data.user,\n        isLoading: false,\n        error: null,\n      });\n\n      console.log('Redirecting to homepage...');\n      router.push('/');\n    } catch (error) {\n      setAuthState((prev) => ({\n        ...prev,\n        isLoading: false,\n        error: 'An unexpected error occurred',\n      }));\n    }\n  };\n\n  const register = async (credentials: RegisterCredentials) => {\n    try {\n      setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));\n\n      // In a real app, this would be an API call to create a new user\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(credentials),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        setAuthState((prev) => ({\n          ...prev,\n          isLoading: false,\n          error: data.message || 'Registration failed',\n        }));\n        return;\n      }\n\n      // Automatically log in after successful registration\n      await login({\n        email: credentials.email,\n        password: credentials.password,\n      });\n    } catch (error) {\n      setAuthState((prev) => ({\n        ...prev,\n        isLoading: false,\n        error: 'An unexpected error occurred',\n      }));\n    }\n  };\n\n  const logout = async () => {\n    try {\n      setAuthState((prev) => ({ ...prev, isLoading: true }));\n\n      // Remove user from localStorage\n      localStorage.removeItem('user');\n\n      // Also clear the cookie\n      document.cookie = 'user=; path=/; max-age=0';\n\n      // Update auth state\n      setAuthState({\n        user: null,\n        isLoading: false,\n        error: null,\n      });\n\n      router.push('/auth/login');\n    } catch (error) {\n      setAuthState((prev) => ({\n        ...prev,\n        isLoading: false,\n        error: 'Logout failed',\n      }));\n    }\n  };\n\n  return (\n    <AuthContext.Provider\n      value={{\n        ...authState,\n        login,\n        register,\n        logout,\n      }}\n    >\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAYA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM;QACN,WAAW;QACX,OAAO;IACT;IAEA,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,MAAM,WAAW,aAAa,OAAO,CAAC;gBACtC,QAAQ,GAAG,CAAC,gCAAgC;gBAE5C,IAAI,UAAU;oBACZ,MAAM,OAAO,KAAK,KAAK,CAAC;oBACxB,QAAQ,GAAG,CAAC,gBAAgB;oBAC5B,aAAa;wBACX;wBACA,WAAW;wBACX,OAAO;oBACT;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,aAAa;wBACX,MAAM;wBACN,WAAW;wBACX,OAAO;oBACT;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,aAAa;oBACX,MAAM;oBACN,WAAW;oBACX,OAAO;gBACT;YACF;QACF;QAEA,kCAAkC;QAClC,uCAAmC;;QAEnC;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO;QACnB,QAAQ,GAAG,CAAC,+BAA+B,YAAY,KAAK;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,aAAa,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK,CAAC;YAEjE,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,QAAQ,GAAG,CAAC,wBAAwB,SAAS,MAAM;YACnD,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,sBAAsB;YAElC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjC,aAAa,CAAC,OAAS,CAAC;wBACtB,GAAG,IAAI;wBACP,WAAW;wBACX,OAAO,KAAK,OAAO,IAAI;oBACzB,CAAC;gBACD;YACF;YAEA,QAAQ,GAAG,CAAC;YACZ,6BAA6B;YAC7B,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,KAAK,IAAI;YAErD,uCAAuC;YACvC,SAAS,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,SAAS,CAAC,KAAK,IAAI,EAAE,uBAAuB,CAAC;YAE5E,QAAQ,GAAG,CAAC;YACZ,oBAAoB;YACpB,aAAa;gBACX,MAAM,KAAK,IAAI;gBACf,WAAW;gBACX,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,aAAa,CAAC,OAAS,CAAC;oBACtB,GAAG,IAAI;oBACP,WAAW;oBACX,OAAO;gBACT,CAAC;QACH;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,aAAa,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK,CAAC;YAEjE,gEAAgE;YAChE,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,aAAa,CAAC,OAAS,CAAC;wBACtB,GAAG,IAAI;wBACP,WAAW;wBACX,OAAO,KAAK,OAAO,IAAI;oBACzB,CAAC;gBACD;YACF;YAEA,qDAAqD;YACrD,MAAM,MAAM;gBACV,OAAO,YAAY,KAAK;gBACxB,UAAU,YAAY,QAAQ;YAChC;QACF,EAAE,OAAO,OAAO;YACd,aAAa,CAAC,OAAS,CAAC;oBACtB,GAAG,IAAI;oBACP,WAAW;oBACX,OAAO;gBACT,CAAC;QACH;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,aAAa,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;gBAAK,CAAC;YAEpD,gCAAgC;YAChC,aAAa,UAAU,CAAC;YAExB,wBAAwB;YACxB,SAAS,MAAM,GAAG;YAElB,oBAAoB;YACpB,aAAa;gBACX,MAAM;gBACN,WAAW;gBACX,OAAO;YACT;YAEA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,aAAa,CAAC,OAAS,CAAC;oBACtB,GAAG,IAAI;oBACP,WAAW;oBACX,OAAO;gBACT,CAAC;QACH;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QACnB,OAAO;YACL,GAAG,SAAS;YACZ;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport {\n  faSearch,\n  faBell,\n  faUser,\n  faGear,\n  faRightFromBracket,\n  faMoon,\n  faSun,\n  faBars\n} from '@fortawesome/free-solid-svg-icons';\n\nexport default function Header() {\n  const { user, logout } = useAuth();\n  const [isProfileOpen, setIsProfileOpen] = useState(false);\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 z-50 w-full\">\n      <div className=\"flex h-16 items-center justify-between px-4 md:px-6\">\n        <div className=\"flex items-center\">\n          {/* Hamburger menu button (visible only on tablet/mobile) */}\n          <button\n            type=\"button\"\n            className=\"p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300\"\n            onClick={() => {\n              if (typeof window !== 'undefined') {\n                // Toggle sidebar-open class for mobile view\n                document.documentElement.classList.toggle('sidebar-open');\n\n                // Dispatch a custom event to notify the sidebar component\n                window.dispatchEvent(new Event('sidebar-toggle'));\n              }\n            }}\n            aria-label=\"Toggle menu\"\n          >\n            <FontAwesomeIcon icon={faBars} className=\"h-6 w-6\" />\n            <span className=\"sr-only\">Toggle sidebar</span>\n          </button>\n\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center ml-4 md:ml-0\">\n            <div className=\"relative h-8 w-8 mr-2\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                className=\"h-8 w-8 text-blue-600\"\n              >\n                <path d=\"M12 2a4 4 0 0 1 4 4v4a4 4 0 0 1-4 4 4 4 0 0 1-4-4V6a4 4 0 0 1 4-4z\" />\n                <path d=\"M18 8a4 4 0 0 1 4 4v2a4 4 0 0 1-4 4 4 4 0 0 1-4-4v-2a4 4 0 0 1 4-4z\" />\n                <path d=\"M6 8a4 4 0 0 1 4 4v2a4 4 0 0 1-4 4 4 4 0 0 1-4-4v-2a4 4 0 0 1 4-4z\" />\n              </svg>\n            </div>\n            <span className=\"text-xl font-semibold text-gray-900 dark:text-white\">AI Hub</span>\n          </Link>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          {/* Search */}\n          <div className=\"hidden md:flex relative\">\n            <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n              <FontAwesomeIcon icon={faSearch} className=\"h-4 w-4 text-gray-400\" />\n            </div>\n            <input\n              type=\"search\"\n              className=\"block w-full rounded-md border border-gray-200 bg-gray-50 py-2 pl-10 pr-3 text-sm text-gray-900 placeholder:text-gray-500 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white dark:placeholder:text-gray-400\"\n              placeholder=\"Search agents...\"\n            />\n          </div>\n\n          {/* Notifications */}\n          <button\n            type=\"button\"\n            className=\"p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300\"\n          >\n            <FontAwesomeIcon icon={faBell} className=\"h-6 w-6\" />\n            <span className=\"sr-only\">Notifications</span>\n          </button>\n\n          {/* Profile dropdown */}\n          <div className=\"relative\">\n            {user ? (\n              <>\n                <button\n                  type=\"button\"\n                  className=\"flex items-center rounded-full\"\n                  onClick={() => setIsProfileOpen(!isProfileOpen)}\n                >\n                  <div className=\"relative h-8 w-8 rounded-full bg-gray-200 overflow-hidden\">\n                    {user.image ? (\n                      <Image\n                        src={user.image}\n                        alt={user.name || 'User'}\n                        width={32}\n                        height={32}\n                        className=\"h-full w-full object-cover\"\n                      />\n                    ) : (\n                      <FontAwesomeIcon icon={faUser} className=\"absolute h-8 w-8 text-gray-400\" />\n                    )}\n                  </div>\n                </button>\n\n                {isProfileOpen && (\n                  <div className=\"absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700\">\n                    <div className=\"border-b border-gray-200 px-4 py-2 dark:border-gray-700\">\n                      <p className=\"text-sm font-medium text-gray-900 dark:text-white\">{user.name}</p>\n                      <p className=\"truncate text-xs text-gray-500 dark:text-gray-400\">{user.email}</p>\n                    </div>\n                    <Link\n                      href=\"/profile\"\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700\"\n                      onClick={() => setIsProfileOpen(false)}\n                    >\n                      Your Profile\n                    </Link>\n                    <Link\n                      href=\"/settings\"\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700\"\n                      onClick={() => setIsProfileOpen(false)}\n                    >\n                      Settings\n                    </Link>\n                    <button\n                      className=\"block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700\"\n                      onClick={() => {\n                        setIsProfileOpen(false);\n                        logout();\n                      }}\n                    >\n                      Sign out\n                    </button>\n                  </div>\n                )}\n              </>\n            ) : (\n              <Link\n                href=\"/auth/login\"\n                className=\"rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800\"\n              >\n                Sign in\n              </Link>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAkBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS;gCACP,uCAAmC;;gCAMnC;4BACF;4BACA,cAAW;;8CAEX,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,SAAM;oCAAE,WAAU;;;;;;8CACzC,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,OAAM;wCACN,SAAQ;wCACR,MAAK;wCACL,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;wCACf,WAAU;;0DAEV,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;8CAGZ,8OAAC;oCAAK,WAAU;8CAAsD;;;;;;;;;;;;;;;;;;8BAI1E,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,WAAQ;wCAAE,WAAU;;;;;;;;;;;8CAE7C,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,8OAAC;4BACC,MAAK;4BACL,WAAU;;8CAEV,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,SAAM;oCAAE,WAAU;;;;;;8CACzC,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,8OAAC;4BAAI,WAAU;sCACZ,qBACC;;kDACE,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,iBAAiB,CAAC;kDAEjC,cAAA,8OAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK,iBACT,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,KAAK;gDACf,KAAK,KAAK,IAAI,IAAI;gDAClB,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;qEAGZ,8OAAC,oKAAA,CAAA,kBAAe;gDAAC,MAAM,wKAAA,CAAA,SAAM;gDAAE,WAAU;;;;;;;;;;;;;;;;oCAK9C,+BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqD,KAAK,IAAI;;;;;;kEAC3E,8OAAC;wDAAE,WAAU;kEAAqD,KAAK,KAAK;;;;;;;;;;;;0DAE9E,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB;0DACjC;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB;0DACjC;;;;;;0DAGD,8OAAC;gDACC,WAAU;gDACV,SAAS;oDACP,iBAAiB;oDACjB;gDACF;0DACD;;;;;;;;;;;;;6DAOP,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/navigation/SimpleSidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport {\n  faHome,\n  faRobot,\n  faComments,\n  faBook,\n  faCog,\n  faUserShield,\n  faChevronDown,\n  faChevronRight,\n  faSearch,\n  faBars,\n  faHeart,\n  faGraduationCap,\n  faUser,\n  faChartLine,\n  faUsers,\n  faVideo,\n  faNewspaper,\n  faFolder,\n  faUpload\n} from '@fortawesome/free-solid-svg-icons';\n\ninterface SidebarItem {\n  id: string;\n  label: string;\n  icon: any;\n  path: string;\n  children?: SidebarItem[];\n}\n\ninterface SidebarSection {\n  id: string;\n  title: string;\n  icon: any;\n  children: SidebarItem[];\n}\n\nconst sidebarSections: SidebarSection[] = [\n  {\n    id: 'dashboard',\n    title: 'Dashboard',\n    icon: faHome,\n    children: [\n      { id: 'home', label: 'Home', icon: faHome, path: '/' }\n    ]\n  },\n  {\n    id: 'agents',\n    title: 'AI Agents',\n    icon: faRobot,\n    children: [\n      { id: 'browse', label: 'Browse Agents', icon: faRobot, path: '/browse' },\n      { id: 'popular', label: 'Popular', icon: faChartLine, path: '/popular' },\n      { id: 'favorites', label: 'Favorites', icon: faHeart, path: '/favorites' }\n    ]\n  },\n  {\n    id: 'conversations',\n    title: 'Conversations',\n    icon: faComments,\n    children: [\n      { id: 'chat', label: 'New Chat', icon: faComments, path: '/conversations' }\n    ]\n  },\n  {\n    id: 'knowledge',\n    title: 'Knowledge & Learning',\n    icon: faBook,\n    children: [\n      { id: 'knowledge', label: 'Knowledge Base', icon: faBook, path: '/knowledge' },\n      { id: 'learning', label: 'Learning Hub', icon: faGraduationCap, path: '/learning' },\n      { id: 'learning-articles', label: 'Articles', icon: faNewspaper, path: '/learning/articles' },\n      { id: 'learning-videos', label: 'Videos', icon: faVideo, path: '/learning/videos' },\n      { id: 'learning-blog', label: 'Blog', icon: faNewspaper, path: '/learning/blog' },\n      { id: 'learning-resources', label: 'Resources', icon: faFolder, path: '/learning/resources' }\n    ]\n  },\n  {\n    id: 'user',\n    title: 'User',\n    icon: faUser,\n    children: [\n      { id: 'profile', label: 'Profile', icon: faUser, path: '/profile' }\n    ]\n  },\n  {\n    id: 'tools',\n    title: 'Tools & Settings',\n    icon: faCog,\n    children: [\n      { id: 'tools', label: 'Tools', icon: faCog, path: '/tools' }\n    ]\n  },\n  {\n    id: 'admin',\n    title: 'Admin',\n    icon: faUserShield,\n    children: [\n      { id: 'admin-dashboard', label: 'Admin Dashboard', icon: faUserShield, path: '/admin' },\n      { id: 'admin-users', label: 'User Management', icon: faUsers, path: '/admin/users' },\n      { id: 'admin-analytics', label: 'Analytics', icon: faChartLine, path: '/admin/analytics' },\n      { id: 'admin-ai-org', label: 'AI Organization', icon: faRobot, path: '/admin/ai-org' }\n    ]\n  }\n];\n\nexport default function SimpleSidebar() {\n  const pathname = usePathname();\n  const [collapsed, setCollapsed] = useState(false);\n  const [collapsedSections, setCollapsedSections] = useState<string[]>([]);\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const toggleSidebar = () => setCollapsed(!collapsed);\n\n  const toggleSection = (sectionId: string) => {\n    setCollapsedSections(prev =>\n      prev.includes(sectionId)\n        ? prev.filter(id => id !== sectionId)\n        : [...prev, sectionId]\n    );\n  };\n\n  const isActive = (path: string) => {\n    if (pathname === path) return true;\n    // Handle nested routes - if current path starts with the nav path (but not root)\n    if (path !== '/' && pathname.startsWith(path)) return true;\n    return false;\n  };\n\n  const isSectionActive = (section: SidebarSection) =>\n    section.children.some(item => isActive(item.path));\n\n  const filteredSections = searchQuery\n    ? sidebarSections.map(section => ({\n        ...section,\n        children: section.children.filter(item =>\n          item.label.toLowerCase().includes(searchQuery.toLowerCase())\n        )\n      })).filter(section => section.children.length > 0)\n    : sidebarSections;\n\n  return (\n    <aside className={`fixed left-0 z-50 flex flex-col h-[calc(100vh-64px)] transition-all duration-300 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 ${\n      collapsed ? 'w-16' : 'w-80'\n    }`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-800\">\n        {!collapsed && (\n          <div className=\"flex items-center space-x-2\">\n            <FontAwesomeIcon icon={faRobot} className=\"h-6 w-6 text-blue-600\" />\n            <span className=\"text-lg font-semibold text-gray-900 dark:text-white\">Navigation</span>\n          </div>\n        )}\n        <button\n          onClick={toggleSidebar}\n          className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\"\n        >\n          <FontAwesomeIcon icon={faBars} className=\"h-4 w-4 text-gray-600 dark:text-gray-400\" />\n        </button>\n      </div>\n\n      {/* Search */}\n      {!collapsed && (\n        <div className=\"p-4 border-b border-gray-200 dark:border-gray-800\">\n          <div className=\"relative\">\n            <FontAwesomeIcon\n              icon={faSearch}\n              className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n            />\n            <input\n              type=\"text\"\n              placeholder=\"Search...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n        </div>\n      )}\n\n      {/* Navigation */}\n      <nav className=\"flex-1 overflow-y-auto p-4 space-y-2\">\n        {filteredSections.map((section) => {\n          const sectionCollapsed = collapsedSections.includes(section.id);\n          const sectionActive = isSectionActive(section);\n\n          return (\n            <div key={section.id} className=\"space-y-1\">\n              {/* Section Header */}\n              <button\n                onClick={() => !collapsed && toggleSection(section.id)}\n                className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors ${\n                  sectionActive\n                    ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'\n                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'\n                }`}\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <FontAwesomeIcon icon={section.icon} className=\"h-5 w-5\" />\n                  {!collapsed && <span className=\"font-medium\">{section.title}</span>}\n                </div>\n                {!collapsed && (\n                  <FontAwesomeIcon\n                    icon={sectionCollapsed ? faChevronRight : faChevronDown}\n                    className=\"h-4 w-4\"\n                  />\n                )}\n              </button>\n\n              {/* Section Items */}\n              {(!sectionCollapsed || collapsed) && (\n                <div className={collapsed ? 'space-y-1' : 'ml-4 space-y-1'}>\n                  {section.children.map((item) => (\n                    <Link\n                      key={item.id}\n                      href={item.path}\n                      className={`flex items-center space-x-3 p-2 rounded-lg transition-colors ${\n                        isActive(item.path)\n                          ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'\n                          : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800/50'\n                      }`}\n                    >\n                      <FontAwesomeIcon icon={item.icon} className=\"h-4 w-4\" />\n                      {!collapsed && <span>{item.label}</span>}\n                    </Link>\n                  ))}\n                </div>\n              )}\n            </div>\n          );\n        })}\n      </nav>\n    </aside>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AA2CA,MAAM,kBAAoC;IACxC;QACE,IAAI;QACJ,OAAO;QACP,MAAM,wKAAA,CAAA,SAAM;QACZ,UAAU;YACR;gBAAE,IAAI;gBAAQ,OAAO;gBAAQ,MAAM,wKAAA,CAAA,SAAM;gBAAE,MAAM;YAAI;SACtD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,wKAAA,CAAA,UAAO;QACb,UAAU;YACR;gBAAE,IAAI;gBAAU,OAAO;gBAAiB,MAAM,wKAAA,CAAA,UAAO;gBAAE,MAAM;YAAU;YACvE;gBAAE,IAAI;gBAAW,OAAO;gBAAW,MAAM,wKAAA,CAAA,cAAW;gBAAE,MAAM;YAAW;YACvE;gBAAE,IAAI;gBAAa,OAAO;gBAAa,MAAM,wKAAA,CAAA,UAAO;gBAAE,MAAM;YAAa;SAC1E;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,wKAAA,CAAA,aAAU;QAChB,UAAU;YACR;gBAAE,IAAI;gBAAQ,OAAO;gBAAY,MAAM,wKAAA,CAAA,aAAU;gBAAE,MAAM;YAAiB;SAC3E;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,wKAAA,CAAA,SAAM;QACZ,UAAU;YACR;gBAAE,IAAI;gBAAa,OAAO;gBAAkB,MAAM,wKAAA,CAAA,SAAM;gBAAE,MAAM;YAAa;YAC7E;gBAAE,IAAI;gBAAY,OAAO;gBAAgB,MAAM,wKAAA,CAAA,kBAAe;gBAAE,MAAM;YAAY;YAClF;gBAAE,IAAI;gBAAqB,OAAO;gBAAY,MAAM,wKAAA,CAAA,cAAW;gBAAE,MAAM;YAAqB;YAC5F;gBAAE,IAAI;gBAAmB,OAAO;gBAAU,MAAM,wKAAA,CAAA,UAAO;gBAAE,MAAM;YAAmB;YAClF;gBAAE,IAAI;gBAAiB,OAAO;gBAAQ,MAAM,wKAAA,CAAA,cAAW;gBAAE,MAAM;YAAiB;YAChF;gBAAE,IAAI;gBAAsB,OAAO;gBAAa,MAAM,wKAAA,CAAA,WAAQ;gBAAE,MAAM;YAAsB;SAC7F;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,wKAAA,CAAA,SAAM;QACZ,UAAU;YACR;gBAAE,IAAI;gBAAW,OAAO;gBAAW,MAAM,wKAAA,CAAA,SAAM;gBAAE,MAAM;YAAW;SACnE;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,wKAAA,CAAA,QAAK;QACX,UAAU;YACR;gBAAE,IAAI;gBAAS,OAAO;gBAAS,MAAM,wKAAA,CAAA,QAAK;gBAAE,MAAM;YAAS;SAC5D;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,wKAAA,CAAA,eAAY;QAClB,UAAU;YACR;gBAAE,IAAI;gBAAmB,OAAO;gBAAmB,MAAM,wKAAA,CAAA,eAAY;gBAAE,MAAM;YAAS;YACtF;gBAAE,IAAI;gBAAe,OAAO;gBAAmB,MAAM,wKAAA,CAAA,UAAO;gBAAE,MAAM;YAAe;YACnF;gBAAE,IAAI;gBAAmB,OAAO;gBAAa,MAAM,wKAAA,CAAA,cAAW;gBAAE,MAAM;YAAmB;YACzF;gBAAE,IAAI;gBAAgB,OAAO;gBAAmB,MAAM,wKAAA,CAAA,UAAO;gBAAE,MAAM;YAAgB;SACtF;IACH;CACD;AAEc,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB,IAAM,aAAa,CAAC;IAE1C,MAAM,gBAAgB,CAAC;QACrB,qBAAqB,CAAA,OACnB,KAAK,QAAQ,CAAC,aACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,aACzB;mBAAI;gBAAM;aAAU;IAE5B;IAEA,MAAM,WAAW,CAAC;QAChB,IAAI,aAAa,MAAM,OAAO;QAC9B,iFAAiF;QACjF,IAAI,SAAS,OAAO,SAAS,UAAU,CAAC,OAAO,OAAO;QACtD,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC,UACvB,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAA,OAAQ,SAAS,KAAK,IAAI;IAElD,MAAM,mBAAmB,cACrB,gBAAgB,GAAG,CAAC,CAAA,UAAW,CAAC;YAC9B,GAAG,OAAO;YACV,UAAU,QAAQ,QAAQ,CAAC,MAAM,CAAC,CAAA,OAChC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAE7D,CAAC,GAAG,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,CAAC,MAAM,GAAG,KAChD;IAEJ,qBACE,8OAAC;QAAM,WAAW,CAAC,yJAAyJ,EAC1K,YAAY,SAAS,QACrB;;0BAEA,8OAAC;gBAAI,WAAU;;oBACZ,CAAC,2BACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oKAAA,CAAA,kBAAe;gCAAC,MAAM,wKAAA,CAAA,UAAO;gCAAE,WAAU;;;;;;0CAC1C,8OAAC;gCAAK,WAAU;0CAAsD;;;;;;;;;;;;kCAG1E,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4BAAC,MAAM,wKAAA,CAAA,SAAM;4BAAE,WAAU;;;;;;;;;;;;;;;;;YAK5C,CAAC,2BACA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oKAAA,CAAA,kBAAe;4BACd,MAAM,wKAAA,CAAA,WAAQ;4BACd,WAAU;;;;;;sCAEZ,8OAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;;;;;;;;;;;;;;;;;0BAOlB,8OAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC;oBACrB,MAAM,mBAAmB,kBAAkB,QAAQ,CAAC,QAAQ,EAAE;oBAC9D,MAAM,gBAAgB,gBAAgB;oBAEtC,qBACE,8OAAC;wBAAqB,WAAU;;0CAE9B,8OAAC;gCACC,SAAS,IAAM,CAAC,aAAa,cAAc,QAAQ,EAAE;gCACrD,WAAW,CAAC,0EAA0E,EACpF,gBACI,oEACA,6EACJ;;kDAEF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oKAAA,CAAA,kBAAe;gDAAC,MAAM,QAAQ,IAAI;gDAAE,WAAU;;;;;;4CAC9C,CAAC,2BAAa,8OAAC;gDAAK,WAAU;0DAAe,QAAQ,KAAK;;;;;;;;;;;;oCAE5D,CAAC,2BACA,8OAAC,oKAAA,CAAA,kBAAe;wCACd,MAAM,mBAAmB,wKAAA,CAAA,iBAAc,GAAG,wKAAA,CAAA,gBAAa;wCACvD,WAAU;;;;;;;;;;;;4BAMf,CAAC,CAAC,oBAAoB,SAAS,mBAC9B,8OAAC;gCAAI,WAAW,YAAY,cAAc;0CACvC,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACrB,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,6DAA6D,EACvE,SAAS,KAAK,IAAI,IACd,qEACA,+EACJ;;0DAEF,8OAAC,oKAAA,CAAA,kBAAe;gDAAC,MAAM,KAAK,IAAI;gDAAE,WAAU;;;;;;4CAC3C,CAAC,2BAAa,8OAAC;0DAAM,KAAK,KAAK;;;;;;;uCAT3B,KAAK,EAAE;;;;;;;;;;;uBA3BZ,QAAQ,EAAE;;;;;gBA2CxB;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 956, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Header from './Header';\nimport SimpleSidebar from '../navigation/SimpleSidebar';\n\nexport default function Layout({ children }: { children: React.ReactNode }) {\n  return (\n    <div className=\"flex min-h-screen flex-col overflow-hidden\">\n      <Header />\n      <div className=\"flex flex-1 pt-16\">\n        <SimpleSidebar />\n        <main className=\"flex-1 transition-all duration-300 w-full overflow-y-auto h-[calc(100vh-64px)]\">\n          <div className=\"container mx-auto px-4 py-8 pb-16 md:px-6 lg:px-8 min-h-[calc(100vh-64px)]\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMe,SAAS,OAAO,EAAE,QAAQ,EAAiC;IACxE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iJAAA,CAAA,UAAa;;;;;kCACd,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1017, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/ConditionalLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { usePathname } from 'next/navigation';\nimport Layout from './Layout';\n\nexport default function ConditionalLayout({ children }: { children: React.ReactNode }) {\n  const pathname = usePathname();\n  \n  // Check if the current path is an auth route\n  const isAuthRoute = pathname?.startsWith('/auth');\n  \n  // If it's an auth route, render children directly without the main layout\n  if (isAuthRoute) {\n    return <>{children}</>;\n  }\n  \n  // For all other routes, use the main layout with header and sidebar\n  return <Layout>{children}</Layout>;\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMe,SAAS,kBAAkB,EAAE,QAAQ,EAAiC;IACnF,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,6CAA6C;IAC7C,MAAM,cAAc,UAAU,WAAW;IAEzC,0EAA0E;IAC1E,IAAI,aAAa;QACf,qBAAO;sBAAG;;IACZ;IAEA,oEAAoE;IACpE,qBAAO,8OAAC,sIAAA,CAAA,UAAM;kBAAE;;;;;;AAClB", "debugId": null}}, {"offset": {"line": 1052, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/providers.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { AuthProvider } from '@/contexts/AuthContext';\n\nexport default function Providers({ children }: { children: React.ReactNode }) {\n  return (\n    <AuthProvider>{children}</AuthProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKe,SAAS,UAAU,EAAE,QAAQ,EAAiC;IAC3E,qBACE,8OAAC,+HAAA,CAAA,eAAY;kBAAE;;;;;;AAEnB", "debugId": null}}]}