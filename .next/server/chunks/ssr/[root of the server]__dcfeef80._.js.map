{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { User, AuthState, LoginCredentials, RegisterCredentials } from '@/types/auth';\n\ninterface AuthContextType extends AuthState {\n  login: (credentials: LoginCredentials) => Promise<void>;\n  register: (credentials: RegisterCredentials) => Promise<void>;\n  logout: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const router = useRouter();\n\n  const [authState, setAuthState] = useState<AuthState>({\n    user: null,\n    isLoading: true,\n    error: null,\n  });\n\n  // Load user from localStorage on mount\n  useEffect(() => {\n    const loadUser = () => {\n      try {\n        console.log('Loading user from localStorage...');\n        const userJson = localStorage.getItem('user');\n        console.log('User JSON from localStorage:', userJson);\n\n        if (userJson) {\n          const user = JSON.parse(userJson) as User;\n          console.log('Parsed user:', user);\n          setAuthState({\n            user,\n            isLoading: false,\n            error: null,\n          });\n        } else {\n          console.log('No user found in localStorage');\n          setAuthState({\n            user: null,\n            isLoading: false,\n            error: null,\n          });\n        }\n      } catch (error) {\n        console.error('Error loading user from localStorage:', error);\n        setAuthState({\n          user: null,\n          isLoading: false,\n          error: 'Failed to load user data',\n        });\n      }\n    };\n\n    // Only run in browser environment\n    if (typeof window !== 'undefined') {\n      loadUser();\n    }\n  }, []);\n\n  const login = async (credentials: LoginCredentials) => {\n    console.log('Login function called with:', credentials.email);\n    try {\n      console.log('Setting loading state...');\n      setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));\n\n      console.log('Sending login request to API...');\n      const response = await fetch('/api/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(credentials),\n      });\n\n      console.log('API response status:', response.status);\n      const data = await response.json();\n      console.log('API response data:', data);\n\n      if (!response.ok || !data.success) {\n        setAuthState((prev) => ({\n          ...prev,\n          isLoading: false,\n          error: data.message || 'Login failed',\n        }));\n        return;\n      }\n\n      console.log('Login successful, storing user data...');\n      // Store user in localStorage\n      localStorage.setItem('user', JSON.stringify(data.user));\n\n      // Also set a cookie for the middleware\n      document.cookie = `user=${JSON.stringify(data.user)}; path=/; max-age=86400`;\n\n      console.log('Updating auth state...');\n      // Update auth state\n      setAuthState({\n        user: data.user,\n        isLoading: false,\n        error: null,\n      });\n\n      console.log('Redirecting to homepage...');\n      router.push('/');\n    } catch (error) {\n      setAuthState((prev) => ({\n        ...prev,\n        isLoading: false,\n        error: 'An unexpected error occurred',\n      }));\n    }\n  };\n\n  const register = async (credentials: RegisterCredentials) => {\n    try {\n      setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));\n\n      // In a real app, this would be an API call to create a new user\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(credentials),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        setAuthState((prev) => ({\n          ...prev,\n          isLoading: false,\n          error: data.message || 'Registration failed',\n        }));\n        return;\n      }\n\n      // Automatically log in after successful registration\n      await login({\n        email: credentials.email,\n        password: credentials.password,\n      });\n    } catch (error) {\n      setAuthState((prev) => ({\n        ...prev,\n        isLoading: false,\n        error: 'An unexpected error occurred',\n      }));\n    }\n  };\n\n  const logout = async () => {\n    try {\n      setAuthState((prev) => ({ ...prev, isLoading: true }));\n\n      // Remove user from localStorage\n      localStorage.removeItem('user');\n\n      // Also clear the cookie\n      document.cookie = 'user=; path=/; max-age=0';\n\n      // Update auth state\n      setAuthState({\n        user: null,\n        isLoading: false,\n        error: null,\n      });\n\n      router.push('/auth/login');\n    } catch (error) {\n      setAuthState((prev) => ({\n        ...prev,\n        isLoading: false,\n        error: 'Logout failed',\n      }));\n    }\n  };\n\n  return (\n    <AuthContext.Provider\n      value={{\n        ...authState,\n        login,\n        register,\n        logout,\n      }}\n    >\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAYA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM;QACN,WAAW;QACX,OAAO;IACT;IAEA,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,MAAM,WAAW,aAAa,OAAO,CAAC;gBACtC,QAAQ,GAAG,CAAC,gCAAgC;gBAE5C,IAAI,UAAU;oBACZ,MAAM,OAAO,KAAK,KAAK,CAAC;oBACxB,QAAQ,GAAG,CAAC,gBAAgB;oBAC5B,aAAa;wBACX;wBACA,WAAW;wBACX,OAAO;oBACT;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,aAAa;wBACX,MAAM;wBACN,WAAW;wBACX,OAAO;oBACT;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,aAAa;oBACX,MAAM;oBACN,WAAW;oBACX,OAAO;gBACT;YACF;QACF;QAEA,kCAAkC;QAClC,uCAAmC;;QAEnC;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO;QACnB,QAAQ,GAAG,CAAC,+BAA+B,YAAY,KAAK;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,aAAa,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK,CAAC;YAEjE,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,QAAQ,GAAG,CAAC,wBAAwB,SAAS,MAAM;YACnD,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,sBAAsB;YAElC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjC,aAAa,CAAC,OAAS,CAAC;wBACtB,GAAG,IAAI;wBACP,WAAW;wBACX,OAAO,KAAK,OAAO,IAAI;oBACzB,CAAC;gBACD;YACF;YAEA,QAAQ,GAAG,CAAC;YACZ,6BAA6B;YAC7B,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,KAAK,IAAI;YAErD,uCAAuC;YACvC,SAAS,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,SAAS,CAAC,KAAK,IAAI,EAAE,uBAAuB,CAAC;YAE5E,QAAQ,GAAG,CAAC;YACZ,oBAAoB;YACpB,aAAa;gBACX,MAAM,KAAK,IAAI;gBACf,WAAW;gBACX,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,aAAa,CAAC,OAAS,CAAC;oBACtB,GAAG,IAAI;oBACP,WAAW;oBACX,OAAO;gBACT,CAAC;QACH;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,aAAa,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK,CAAC;YAEjE,gEAAgE;YAChE,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,aAAa,CAAC,OAAS,CAAC;wBACtB,GAAG,IAAI;wBACP,WAAW;wBACX,OAAO,KAAK,OAAO,IAAI;oBACzB,CAAC;gBACD;YACF;YAEA,qDAAqD;YACrD,MAAM,MAAM;gBACV,OAAO,YAAY,KAAK;gBACxB,UAAU,YAAY,QAAQ;YAChC;QACF,EAAE,OAAO,OAAO;YACd,aAAa,CAAC,OAAS,CAAC;oBACtB,GAAG,IAAI;oBACP,WAAW;oBACX,OAAO;gBACT,CAAC;QACH;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,aAAa,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;gBAAK,CAAC;YAEpD,gCAAgC;YAChC,aAAa,UAAU,CAAC;YAExB,wBAAwB;YACxB,SAAS,MAAM,GAAG;YAElB,oBAAoB;YACpB,aAAa;gBACX,MAAM;gBACN,WAAW;gBACX,OAAO;YACT;YAEA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,aAAa,CAAC,OAAS,CAAC;oBACtB,GAAG,IAAI;oBACP,WAAW;oBACX,OAAO;gBACT,CAAC;QACH;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QACnB,OAAO;YACL,GAAG,SAAS;YACZ;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport {\n  faSearch,\n  faBell,\n  faUser,\n  faGear,\n  faRightFromBracket,\n  faMoon,\n  faSun,\n  faBars\n} from '@fortawesome/free-solid-svg-icons';\n\nexport default function Header() {\n  const { user, logout } = useAuth();\n  const [isProfileOpen, setIsProfileOpen] = useState(false);\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 z-50 w-full\">\n      <div className=\"flex h-16 items-center justify-between px-4 md:px-6\">\n        <div className=\"flex items-center\">\n          {/* Hamburger menu button (visible only on tablet/mobile) */}\n          <button\n            type=\"button\"\n            className=\"p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300\"\n            onClick={() => {\n              if (typeof window !== 'undefined') {\n                // Toggle sidebar-open class for mobile view\n                document.documentElement.classList.toggle('sidebar-open');\n\n                // Dispatch a custom event to notify the sidebar component\n                window.dispatchEvent(new Event('sidebar-toggle'));\n              }\n            }}\n            aria-label=\"Toggle menu\"\n          >\n            <FontAwesomeIcon icon={faBars} className=\"h-6 w-6\" />\n            <span className=\"sr-only\">Toggle sidebar</span>\n          </button>\n\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center ml-4 md:ml-0\">\n            <div className=\"relative h-8 w-8 mr-2\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                className=\"h-8 w-8 text-blue-600\"\n              >\n                <path d=\"M12 2a4 4 0 0 1 4 4v4a4 4 0 0 1-4 4 4 4 0 0 1-4-4V6a4 4 0 0 1 4-4z\" />\n                <path d=\"M18 8a4 4 0 0 1 4 4v2a4 4 0 0 1-4 4 4 4 0 0 1-4-4v-2a4 4 0 0 1 4-4z\" />\n                <path d=\"M6 8a4 4 0 0 1 4 4v2a4 4 0 0 1-4 4 4 4 0 0 1-4-4v-2a4 4 0 0 1 4-4z\" />\n              </svg>\n            </div>\n            <span className=\"text-xl font-semibold text-gray-900 dark:text-white\">AI Hub</span>\n          </Link>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          {/* Search */}\n          <div className=\"hidden md:flex relative\">\n            <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n              <FontAwesomeIcon icon={faSearch} className=\"h-4 w-4 text-gray-400\" />\n            </div>\n            <input\n              type=\"search\"\n              className=\"block w-full rounded-md border border-gray-200 bg-gray-50 py-2 pl-10 pr-3 text-sm text-gray-900 placeholder:text-gray-500 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white dark:placeholder:text-gray-400\"\n              placeholder=\"Search agents...\"\n            />\n          </div>\n\n          {/* Notifications */}\n          <button\n            type=\"button\"\n            className=\"p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300\"\n          >\n            <FontAwesomeIcon icon={faBell} className=\"h-6 w-6\" />\n            <span className=\"sr-only\">Notifications</span>\n          </button>\n\n          {/* Profile dropdown */}\n          <div className=\"relative\">\n            {user ? (\n              <>\n                <button\n                  type=\"button\"\n                  className=\"flex items-center rounded-full\"\n                  onClick={() => setIsProfileOpen(!isProfileOpen)}\n                >\n                  <div className=\"relative h-8 w-8 rounded-full bg-gray-200 overflow-hidden\">\n                    {user.image ? (\n                      <Image\n                        src={user.image}\n                        alt={user.name || 'User'}\n                        width={32}\n                        height={32}\n                        className=\"h-full w-full object-cover\"\n                      />\n                    ) : (\n                      <FontAwesomeIcon icon={faUser} className=\"absolute h-8 w-8 text-gray-400\" />\n                    )}\n                  </div>\n                </button>\n\n                {isProfileOpen && (\n                  <div className=\"absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700\">\n                    <div className=\"border-b border-gray-200 px-4 py-2 dark:border-gray-700\">\n                      <p className=\"text-sm font-medium text-gray-900 dark:text-white\">{user.name}</p>\n                      <p className=\"truncate text-xs text-gray-500 dark:text-gray-400\">{user.email}</p>\n                    </div>\n                    <Link\n                      href=\"/profile\"\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700\"\n                      onClick={() => setIsProfileOpen(false)}\n                    >\n                      Your Profile\n                    </Link>\n                    <Link\n                      href=\"/settings\"\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700\"\n                      onClick={() => setIsProfileOpen(false)}\n                    >\n                      Settings\n                    </Link>\n                    <button\n                      className=\"block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700\"\n                      onClick={() => {\n                        setIsProfileOpen(false);\n                        logout();\n                      }}\n                    >\n                      Sign out\n                    </button>\n                  </div>\n                )}\n              </>\n            ) : (\n              <Link\n                href=\"/auth/login\"\n                className=\"rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800\"\n              >\n                Sign in\n              </Link>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAkBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS;gCACP,uCAAmC;;gCAMnC;4BACF;4BACA,cAAW;;8CAEX,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,SAAM;oCAAE,WAAU;;;;;;8CACzC,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,OAAM;wCACN,SAAQ;wCACR,MAAK;wCACL,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;wCACf,WAAU;;0DAEV,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;8CAGZ,8OAAC;oCAAK,WAAU;8CAAsD;;;;;;;;;;;;;;;;;;8BAI1E,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,WAAQ;wCAAE,WAAU;;;;;;;;;;;8CAE7C,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,8OAAC;4BACC,MAAK;4BACL,WAAU;;8CAEV,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,SAAM;oCAAE,WAAU;;;;;;8CACzC,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,8OAAC;4BAAI,WAAU;sCACZ,qBACC;;kDACE,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,iBAAiB,CAAC;kDAEjC,cAAA,8OAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK,iBACT,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,KAAK;gDACf,KAAK,KAAK,IAAI,IAAI;gDAClB,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;qEAGZ,8OAAC,oKAAA,CAAA,kBAAe;gDAAC,MAAM,wKAAA,CAAA,SAAM;gDAAE,WAAU;;;;;;;;;;;;;;;;oCAK9C,+BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqD,KAAK,IAAI;;;;;;kEAC3E,8OAAC;wDAAE,WAAU;kEAAqD,KAAK,KAAK;;;;;;;;;;;;0DAE9E,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB;0DACjC;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB;0DACjC;;;;;;0DAGD,8OAAC;gDACC,WAAU;gDACV,SAAS;oDACP,iBAAiB;oDACjB;gDACF;0DACD;;;;;;;;;;;;;6DAOP,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/navigation/SimpleSidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faHome, faRobot, faComments, faBook, faCog } from '@fortawesome/free-solid-svg-icons';\n\nexport default function SimpleSidebar() {\n  const pathname = usePathname();\n\n  const menuItems = [\n    { id: 'home', label: 'Home', icon: faHome, path: '/' },\n    { id: 'agents', label: 'Agents', icon: faRobot, path: '/browse' },\n    { id: 'conversations', label: 'Conversations', icon: faComments, path: '/conversations' },\n    { id: 'knowledge', label: 'Knowledge', icon: faBook, path: '/knowledge' },\n    { id: 'tools', label: 'Tools', icon: faCog, path: '/tools' }\n  ];\n\n  return (\n    <aside className=\"fixed left-0 z-50 flex flex-col h-[calc(100vh-64px)] w-80 transition-all duration-300 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800\">\n      {/* Sidebar Header */}\n      <div className=\"flex h-16 items-center justify-between border-b border-gray-200/30 px-4 dark:border-gray-800/30 mt-2\">\n        <span className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n          AI Hub\n        </span>\n      </div>\n\n      {/* Navigation Content */}\n      <div className=\"flex-1 overflow-y-auto p-4\">\n        <nav className=\"space-y-2\">\n          {menuItems.map((item) => (\n            <Link\n              key={item.id}\n              href={item.path}\n              className={`flex items-center px-3 py-2 text-sm rounded-md transition-colors ${\n                pathname === item.path\n                  ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'\n                  : 'text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800'\n              }`}\n            >\n              <FontAwesomeIcon icon={item.icon} className=\"h-4 w-4 mr-3\" />\n              <span>{item.label}</span>\n            </Link>\n          ))}\n        </nav>\n      </div>\n    </aside>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAQe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,YAAY;QAChB;YAAE,IAAI;YAAQ,OAAO;YAAQ,MAAM,wKAAA,CAAA,SAAM;YAAE,MAAM;QAAI;QACrD;YAAE,IAAI;YAAU,OAAO;YAAU,MAAM,wKAAA,CAAA,UAAO;YAAE,MAAM;QAAU;QAChE;YAAE,IAAI;YAAiB,OAAO;YAAiB,MAAM,wKAAA,CAAA,aAAU;YAAE,MAAM;QAAiB;QACxF;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,wKAAA,CAAA,SAAM;YAAE,MAAM;QAAa;QACxE;YAAE,IAAI;YAAS,OAAO;YAAS,MAAM,wKAAA,CAAA,QAAK;YAAE,MAAM;QAAS;KAC5D;IAED,qBACE,8OAAC;QAAM,WAAU;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;8BAAsD;;;;;;;;;;;0BAMxE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAC,iEAAiE,EAC3E,aAAa,KAAK,IAAI,GAClB,oEACA,6EACJ;;8CAEF,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,KAAK,IAAI;oCAAE,WAAU;;;;;;8CAC5C,8OAAC;8CAAM,KAAK,KAAK;;;;;;;2BATZ,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;AAgB1B", "debugId": null}}, {"offset": {"line": 686, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/types/navigation.ts"], "sourcesContent": ["// Enhanced Navigation Types for Open WebUI Integration\n\nexport interface SidebarSection {\n  id: string;\n  title: string;\n  icon: string;\n  isCollapsed: boolean;\n  badgeCount?: number;\n  children: SidebarItem[];\n  permissions?: string[];\n  order: number;\n}\n\nexport interface SidebarItem {\n  id: string;\n  label: string;\n  icon?: string;\n  path: string;\n  isActive?: boolean;\n  badge?: string | number;\n  onClick?: () => void;\n  permissions?: string[];\n  isNew?: boolean;\n  children?: SidebarItem[];\n}\n\nexport interface QuickAction {\n  id: string;\n  icon: string;\n  label: string;\n  action: () => void;\n  contexts: string[]; // ['chat', 'knowledge', 'agents', '*']\n  position: 'floating' | 'sidebar';\n  priority: number;\n  isVisible?: boolean;\n  badge?: string | number;\n}\n\nexport interface NavigationState {\n  activeSection: string | null;\n  activePath: string;\n  collapsedSections: string[];\n  sidebarCollapsed: boolean;\n  quickActions: QuickAction[];\n  context: NavigationContext;\n}\n\nexport interface NavigationContext {\n  currentArea: 'dashboard' | 'agents' | 'conversations' | 'knowledge' | 'tools' | 'admin';\n  activeChat?: string;\n  hasActiveSession?: boolean;\n  isInKnowledgeArea?: boolean;\n  userRole?: 'user' | 'admin' | 'moderator';\n}\n\nexport interface ChatSession {\n  id: string;\n  title: string;\n  agentId?: string;\n  agentName?: string;\n  lastMessage?: string;\n  timestamp: number;\n  isActive: boolean;\n  isPinned?: boolean;\n  folder?: string;\n  unreadCount?: number;\n}\n\nexport interface DocumentItem {\n  id: string;\n  name: string;\n  type: string;\n  size: number;\n  uploadedAt: number;\n  isProcessed: boolean;\n  tags?: string[];\n}\n\nexport interface NavigationConfig {\n  enableQuickActions: boolean;\n  enableBadges: boolean;\n  enableAnimations: boolean;\n  persistState: boolean;\n  mobileBreakpoint: number;\n  tabletBreakpoint: number;\n}\n\n// Navigation Events\nexport type NavigationEvent = \n  | { type: 'SECTION_TOGGLE'; sectionId: string }\n  | { type: 'SIDEBAR_TOGGLE' }\n  | { type: 'CONTEXT_CHANGE'; context: NavigationContext }\n  | { type: 'QUICK_ACTION'; actionId: string }\n  | { type: 'CHAT_SELECT'; chatId: string }\n  | { type: 'DOCUMENT_SELECT'; documentId: string };\n\n// Responsive Breakpoints\nexport const BREAKPOINTS = {\n  mobile: 768,\n  tablet: 1024,\n  desktop: 1280\n} as const;\n\n// Default Navigation Configuration\nexport const DEFAULT_NAV_CONFIG: NavigationConfig = {\n  enableQuickActions: true,\n  enableBadges: true,\n  enableAnimations: true,\n  persistState: true,\n  mobileBreakpoint: BREAKPOINTS.mobile,\n  tabletBreakpoint: BREAKPOINTS.tablet\n};\n\n// Permission Levels\nexport type PermissionLevel = 'public' | 'user' | 'admin' | 'moderator';\n\n// Navigation Item Groups\nexport type NavigationGroup = \n  | 'dashboard'\n  | 'agents' \n  | 'conversations'\n  | 'knowledge'\n  | 'tools'\n  | 'admin';\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;;AAiGhD,MAAM,cAAc;IACzB,QAAQ;IACR,QAAQ;IACR,SAAS;AACX;AAGO,MAAM,qBAAuC;IAClD,oBAAoB;IACpB,cAAc;IACd,kBAAkB;IAClB,cAAc;IACd,kBAAkB,YAAY,MAAM;IACpC,kBAAkB,YAAY,MAAM;AACtC", "debugId": null}}, {"offset": {"line": 710, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/debug/RenderTracker.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\ninterface RenderTrackerProps {\n  name: string;\n  props?: Record<string, any>;\n}\n\nexport default function RenderTracker({ name, props = {} }: RenderTrackerProps) {\n  const renderCount = useRef(0);\n  const prevProps = useRef(props);\n\n  renderCount.current += 1;\n\n  useEffect(() => {\n    console.log(`🔄 ${name} rendered ${renderCount.current} times`);\n    \n    if (renderCount.current > 10) {\n      console.error(`⚠️ ${name} has rendered ${renderCount.current} times - possible infinite loop!`);\n      console.log('Current props:', props);\n      console.log('Previous props:', prevProps.current);\n      \n      // Check which props changed\n      const changedProps: string[] = [];\n      Object.keys(props).forEach(key => {\n        if (props[key] !== prevProps.current[key]) {\n          changedProps.push(key);\n        }\n      });\n      \n      if (changedProps.length > 0) {\n        console.log('Changed props:', changedProps);\n      }\n    }\n    \n    prevProps.current = props;\n  });\n\n  return null;\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AASe,SAAS,cAAc,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAsB;IAC5E,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEzB,YAAY,OAAO,IAAI;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,UAAU,EAAE,YAAY,OAAO,CAAC,MAAM,CAAC;QAE9D,IAAI,YAAY,OAAO,GAAG,IAAI;YAC5B,QAAQ,KAAK,CAAC,CAAC,GAAG,EAAE,KAAK,cAAc,EAAE,YAAY,OAAO,CAAC,gCAAgC,CAAC;YAC9F,QAAQ,GAAG,CAAC,kBAAkB;YAC9B,QAAQ,GAAG,CAAC,mBAAmB,UAAU,OAAO;YAEhD,4BAA4B;YAC5B,MAAM,eAAyB,EAAE;YACjC,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAA;gBACzB,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,OAAO,CAAC,IAAI,EAAE;oBACzC,aAAa,IAAI,CAAC;gBACpB;YACF;YAEA,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,QAAQ,GAAG,CAAC,kBAAkB;YAChC;QACF;QAEA,UAAU,OAAO,GAAG;IACtB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 747, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/contexts/NavigationContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useReducer, useEffect, useRef, ReactNode } from 'react';\nimport { usePathname } from 'next/navigation';\nimport {\n  NavigationState,\n  NavigationEvent,\n  NavigationContext as NavContextType,\n  QuickAction,\n  ChatSession,\n  DEFAULT_NAV_CONFIG\n} from '@/types/navigation';\nimport RenderTracker from '@/components/debug/RenderTracker';\n\n// Initial state\nconst initialState: NavigationState = {\n  activeSection: null,\n  activePath: '/',\n  collapsedSections: [],\n  sidebarCollapsed: false,\n  quickActions: [],\n  context: {\n    currentArea: 'dashboard',\n    hasActiveSession: false,\n    isInKnowledgeArea: false,\n    userRole: 'user'\n  }\n};\n\n// Navigation reducer\nfunction navigationReducer(state: NavigationState, action: NavigationEvent): NavigationState {\n  switch (action.type) {\n    case 'SECTION_TOGGLE':\n      const isCollapsed = state.collapsedSections.includes(action.sectionId);\n      return {\n        ...state,\n        collapsedSections: isCollapsed\n          ? state.collapsedSections.filter(id => id !== action.sectionId)\n          : [...state.collapsedSections, action.sectionId]\n      };\n\n    case 'SIDEBAR_TOGGLE':\n      return {\n        ...state,\n        sidebarCollapsed: !state.sidebarCollapsed\n      };\n\n    case 'CONTEXT_CHANGE':\n      return {\n        ...state,\n        context: { ...state.context, ...action.context }\n      };\n\n    case 'QUICK_ACTION':\n      // Handle quick action execution\n      const action_item = state.quickActions.find(qa => qa.id === action.actionId);\n      if (action_item) {\n        action_item.action();\n      }\n      return state;\n\n    case 'CHAT_SELECT':\n      return {\n        ...state,\n        context: {\n          ...state.context,\n          activeChat: action.chatId,\n          hasActiveSession: true,\n          currentArea: 'conversations'\n        }\n      };\n\n    case 'DOCUMENT_SELECT':\n      return {\n        ...state,\n        context: {\n          ...state.context,\n          currentArea: 'knowledge',\n          isInKnowledgeArea: true\n        }\n      };\n\n    default:\n      return state;\n  }\n}\n\n// Context type\ninterface NavigationContextType {\n  state: NavigationState;\n  dispatch: React.Dispatch<NavigationEvent>;\n  toggleSection: (sectionId: string) => void;\n  toggleSidebar: () => void;\n  updateContext: (context: Partial<NavContextType>) => void;\n  executeQuickAction: (actionId: string) => void;\n  selectChat: (chatId: string) => void;\n  selectDocument: (documentId: string) => void;\n  getVisibleQuickActions: () => QuickAction[];\n  isCurrentArea: (area: string) => boolean;\n}\n\n// Create context\nconst NavigationContext = createContext<NavigationContextType | undefined>(undefined);\n\n// Provider component\nexport function NavigationProvider({ children }: { children: ReactNode }) {\n  const [state, dispatch] = useReducer(navigationReducer, initialState);\n  const pathname = usePathname();\n  const previousAreaRef = useRef<string>('dashboard');\n  const previousKnowledgeRef = useRef<boolean>(false);\n\n  // Update active path and context based on current route\n  useEffect(() => {\n    let currentArea: NavContextType['currentArea'] = 'dashboard';\n    let isInKnowledgeArea = false;\n\n    if (pathname.startsWith('/browse') || pathname.startsWith('/popular') || pathname.startsWith('/favorites')) {\n      currentArea = 'agents';\n    } else if (pathname.startsWith('/conversations') || pathname.startsWith('/chat')) {\n      currentArea = 'conversations';\n    } else if (pathname.startsWith('/knowledge') || pathname.startsWith('/documents') || pathname.startsWith('/learning')) {\n      currentArea = 'knowledge';\n      isInKnowledgeArea = true;\n    } else if (pathname.startsWith('/tools') || pathname.startsWith('/settings')) {\n      currentArea = 'tools';\n    } else if (pathname.startsWith('/admin')) {\n      currentArea = 'admin';\n    }\n\n    // Only dispatch if the area actually changed\n    if (previousAreaRef.current !== currentArea || previousKnowledgeRef.current !== isInKnowledgeArea) {\n      previousAreaRef.current = currentArea;\n      previousKnowledgeRef.current = isInKnowledgeArea;\n\n      dispatch({\n        type: 'CONTEXT_CHANGE',\n        context: {\n          currentArea,\n          isInKnowledgeArea\n        }\n      });\n    }\n  }, [pathname]);\n\n  // Load persisted state from localStorage (only once on mount)\n  useEffect(() => {\n    if (typeof window !== 'undefined' && DEFAULT_NAV_CONFIG.persistState) {\n      const savedCollapsed = localStorage.getItem('sidebarCollapsed');\n      const savedSections = localStorage.getItem('collapsedSections');\n\n      if (savedCollapsed === 'true') {\n        dispatch({ type: 'SIDEBAR_TOGGLE' });\n      }\n\n      if (savedSections) {\n        try {\n          const sections = JSON.parse(savedSections);\n          if (Array.isArray(sections)) {\n            sections.forEach((sectionId: string) => {\n              dispatch({ type: 'SECTION_TOGGLE', sectionId });\n            });\n          }\n        } catch (error) {\n          console.warn('Failed to parse saved collapsed sections:', error);\n        }\n      }\n    }\n  }, []); // Empty dependency array - only run once on mount\n\n  // Persist state changes to localStorage\n  useEffect(() => {\n    if (typeof window !== 'undefined' && DEFAULT_NAV_CONFIG.persistState) {\n      localStorage.setItem('sidebarCollapsed', state.sidebarCollapsed.toString());\n      localStorage.setItem('collapsedSections', JSON.stringify(state.collapsedSections));\n    }\n  }, [state.sidebarCollapsed, state.collapsedSections]);\n\n  // Context methods\n  const toggleSection = (sectionId: string) => {\n    dispatch({ type: 'SECTION_TOGGLE', sectionId });\n  };\n\n  const toggleSidebar = () => {\n    dispatch({ type: 'SIDEBAR_TOGGLE' });\n  };\n\n  const updateContext = (context: Partial<NavContextType>) => {\n    dispatch({ type: 'CONTEXT_CHANGE', context });\n  };\n\n  const executeQuickAction = (actionId: string) => {\n    dispatch({ type: 'QUICK_ACTION', actionId });\n  };\n\n  const selectChat = (chatId: string) => {\n    dispatch({ type: 'CHAT_SELECT', chatId });\n  };\n\n  const selectDocument = (documentId: string) => {\n    dispatch({ type: 'DOCUMENT_SELECT', documentId });\n  };\n\n  const getVisibleQuickActions = (): QuickAction[] => {\n    return state.quickActions.filter(action => {\n      if (action.contexts.includes('*')) return true;\n      return action.contexts.includes(state.context.currentArea);\n    });\n  };\n\n  const isCurrentArea = (area: string): boolean => {\n    return state.context.currentArea === area;\n  };\n\n  const value: NavigationContextType = {\n    state,\n    dispatch,\n    toggleSection,\n    toggleSidebar,\n    updateContext,\n    executeQuickAction,\n    selectChat,\n    selectDocument,\n    getVisibleQuickActions,\n    isCurrentArea\n  };\n\n  return (\n    <NavigationContext.Provider value={value}>\n      <RenderTracker\n        name=\"NavigationProvider\"\n        props={{\n          pathname,\n          currentArea: state.context.currentArea,\n          collapsedSections: state.collapsedSections,\n          sidebarCollapsed: state.sidebarCollapsed\n        }}\n      />\n      {children}\n    </NavigationContext.Provider>\n  );\n}\n\n// Hook to use navigation context\nexport function useNavigation() {\n  const context = useContext(NavigationContext);\n  if (context === undefined) {\n    throw new Error('useNavigation must be used within a NavigationProvider');\n  }\n  return context;\n}\n\n// Hook for quick actions\nexport function useQuickActions() {\n  const { state, executeQuickAction, getVisibleQuickActions } = useNavigation();\n\n  return {\n    quickActions: getVisibleQuickActions(),\n    executeAction: executeQuickAction,\n    context: state.context\n  };\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAQA;AAZA;;;;;;AAcA,gBAAgB;AAChB,MAAM,eAAgC;IACpC,eAAe;IACf,YAAY;IACZ,mBAAmB,EAAE;IACrB,kBAAkB;IAClB,cAAc,EAAE;IAChB,SAAS;QACP,aAAa;QACb,kBAAkB;QAClB,mBAAmB;QACnB,UAAU;IACZ;AACF;AAEA,qBAAqB;AACrB,SAAS,kBAAkB,KAAsB,EAAE,MAAuB;IACxE,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,MAAM,cAAc,MAAM,iBAAiB,CAAC,QAAQ,CAAC,OAAO,SAAS;YACrE,OAAO;gBACL,GAAG,KAAK;gBACR,mBAAmB,cACf,MAAM,iBAAiB,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO,OAAO,SAAS,IAC5D;uBAAI,MAAM,iBAAiB;oBAAE,OAAO,SAAS;iBAAC;YACpD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,kBAAkB,CAAC,MAAM,gBAAgB;YAC3C;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,SAAS;oBAAE,GAAG,MAAM,OAAO;oBAAE,GAAG,OAAO,OAAO;gBAAC;YACjD;QAEF,KAAK;YACH,gCAAgC;YAChC,MAAM,cAAc,MAAM,YAAY,CAAC,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK,OAAO,QAAQ;YAC3E,IAAI,aAAa;gBACf,YAAY,MAAM;YACpB;YACA,OAAO;QAET,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,SAAS;oBACP,GAAG,MAAM,OAAO;oBAChB,YAAY,OAAO,MAAM;oBACzB,kBAAkB;oBAClB,aAAa;gBACf;YACF;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,SAAS;oBACP,GAAG,MAAM,OAAO;oBAChB,aAAa;oBACb,mBAAmB;gBACrB;YACF;QAEF;YACE,OAAO;IACX;AACF;AAgBA,iBAAiB;AACjB,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAqC;AAGpE,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,mBAAmB;IACxD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU;IACvC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAW;IAE7C,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAA6C;QACjD,IAAI,oBAAoB;QAExB,IAAI,SAAS,UAAU,CAAC,cAAc,SAAS,UAAU,CAAC,eAAe,SAAS,UAAU,CAAC,eAAe;YAC1G,cAAc;QAChB,OAAO,IAAI,SAAS,UAAU,CAAC,qBAAqB,SAAS,UAAU,CAAC,UAAU;YAChF,cAAc;QAChB,OAAO,IAAI,SAAS,UAAU,CAAC,iBAAiB,SAAS,UAAU,CAAC,iBAAiB,SAAS,UAAU,CAAC,cAAc;YACrH,cAAc;YACd,oBAAoB;QACtB,OAAO,IAAI,SAAS,UAAU,CAAC,aAAa,SAAS,UAAU,CAAC,cAAc;YAC5E,cAAc;QAChB,OAAO,IAAI,SAAS,UAAU,CAAC,WAAW;YACxC,cAAc;QAChB;QAEA,6CAA6C;QAC7C,IAAI,gBAAgB,OAAO,KAAK,eAAe,qBAAqB,OAAO,KAAK,mBAAmB;YACjG,gBAAgB,OAAO,GAAG;YAC1B,qBAAqB,OAAO,GAAG;YAE/B,SAAS;gBACP,MAAM;gBACN,SAAS;oBACP;oBACA;gBACF;YACF;QACF;IACF,GAAG;QAAC;KAAS;IAEb,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAsE;;QAoBtE;IACF,GAAG,EAAE,GAAG,kDAAkD;IAE1D,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAsE;;QAGtE;IACF,GAAG;QAAC,MAAM,gBAAgB;QAAE,MAAM,iBAAiB;KAAC;IAEpD,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,SAAS;YAAE,MAAM;YAAkB;QAAU;IAC/C;IAEA,MAAM,gBAAgB;QACpB,SAAS;YAAE,MAAM;QAAiB;IACpC;IAEA,MAAM,gBAAgB,CAAC;QACrB,SAAS;YAAE,MAAM;YAAkB;QAAQ;IAC7C;IAEA,MAAM,qBAAqB,CAAC;QAC1B,SAAS;YAAE,MAAM;YAAgB;QAAS;IAC5C;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS;YAAE,MAAM;YAAe;QAAO;IACzC;IAEA,MAAM,iBAAiB,CAAC;QACtB,SAAS;YAAE,MAAM;YAAmB;QAAW;IACjD;IAEA,MAAM,yBAAyB;QAC7B,OAAO,MAAM,YAAY,CAAC,MAAM,CAAC,CAAA;YAC/B,IAAI,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM,OAAO;YAC1C,OAAO,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM,OAAO,CAAC,WAAW;QAC3D;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,MAAM,OAAO,CAAC,WAAW,KAAK;IACvC;IAEA,MAAM,QAA+B;QACnC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,kBAAkB,QAAQ;QAAC,OAAO;;0BACjC,8OAAC,4IAAA,CAAA,UAAa;gBACZ,MAAK;gBACL,OAAO;oBACL;oBACA,aAAa,MAAM,OAAO,CAAC,WAAW;oBACtC,mBAAmB,MAAM,iBAAiB;oBAC1C,kBAAkB,MAAM,gBAAgB;gBAC1C;;;;;;YAED;;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,GAAG;IAE9D,OAAO;QACL,cAAc;QACd,eAAe;QACf,SAAS,MAAM,OAAO;IACxB;AACF", "debugId": null}}, {"offset": {"line": 987, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport Header from './Header';\n// import EnhancedSidebar from '../navigation/EnhancedSidebar';\nimport SimpleSidebar from '../navigation/SimpleSidebar';\n// import QuickActions from '../navigation/QuickActions';\nimport { NavigationProvider } from '@/contexts/NavigationContext';\n\nexport default function Layout({ children }: { children: React.ReactNode }) {\n  // Simplified layout - removed old event listeners that might conflict with new navigation\n\n  return (\n    <NavigationProvider>\n      <div className=\"flex min-h-screen flex-col overflow-hidden\">\n        <Header />\n        <div className=\"flex flex-1 pt-16\">\n          <SimpleSidebar />\n          <main\n            className=\"flex-1 transition-all duration-300 w-full overflow-y-auto h-[calc(100vh-64px)] ml-80\"\n          >\n            <div className=\"container mx-auto px-4 py-8 pb-16 md:px-6 lg:px-8 min-h-[calc(100vh-64px)]\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n    </NavigationProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA,+DAA+D;AAC/D;AACA,yDAAyD;AACzD;AAPA;;;;;AASe,SAAS,OAAO,EAAE,QAAQ,EAAiC;IACxE,0FAA0F;IAE1F,qBACE,8OAAC,qIAAA,CAAA,qBAAkB;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iJAAA,CAAA,UAAa;;;;;sCACd,8OAAC;4BACC,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 1059, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/ConditionalLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { usePathname } from 'next/navigation';\nimport Layout from './Layout';\n\nexport default function ConditionalLayout({ children }: { children: React.ReactNode }) {\n  const pathname = usePathname();\n  \n  // Check if the current path is an auth route\n  const isAuthRoute = pathname?.startsWith('/auth');\n  \n  // If it's an auth route, render children directly without the main layout\n  if (isAuthRoute) {\n    return <>{children}</>;\n  }\n  \n  // For all other routes, use the main layout with header and sidebar\n  return <Layout>{children}</Layout>;\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMe,SAAS,kBAAkB,EAAE,QAAQ,EAAiC;IACnF,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,6CAA6C;IAC7C,MAAM,cAAc,UAAU,WAAW;IAEzC,0EAA0E;IAC1E,IAAI,aAAa;QACf,qBAAO;sBAAG;;IACZ;IAEA,oEAAoE;IACpE,qBAAO,8OAAC,sIAAA,CAAA,UAAM;kBAAE;;;;;;AAClB", "debugId": null}}, {"offset": {"line": 1094, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/providers.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { AuthProvider } from '@/contexts/AuthContext';\n\nexport default function Providers({ children }: { children: React.ReactNode }) {\n  return (\n    <AuthProvider>{children}</AuthProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKe,SAAS,UAAU,EAAE,QAAQ,EAAiC;IAC3E,qBACE,8OAAC,+HAAA,CAAA,eAAY;kBAAE;;;;;;AAEnB", "debugId": null}}]}