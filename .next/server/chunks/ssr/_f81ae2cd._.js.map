{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/conversations/page.tsx"], "sourcesContent": ["import React from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faComments, faPlus, faHistory, faThumbtack } from '@fortawesome/free-solid-svg-icons';\n\nexport default function ConversationsPage() {\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"border-b border-gray-200 pb-4 dark:border-gray-700\">\n        <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n          <FontAwesomeIcon icon={faComments} className=\"mr-3\" />\n          Conversations\n        </h1>\n        <p className=\"mt-2 text-gray-600 dark:text-gray-400\">\n          Manage your AI conversations and chat sessions\n        </p>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                Active Sessions\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                2 ongoing conversations\n              </p>\n            </div>\n            <FontAwesomeIcon icon={faComments} className=\"h-8 w-8 text-blue-500\" />\n          </div>\n          <button className=\"mt-4 w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\">\n            View Active Sessions\n          </button>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                Chat History\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                Browse past conversations\n              </p>\n            </div>\n            <FontAwesomeIcon icon={faHistory} className=\"h-8 w-8 text-green-500\" />\n          </div>\n          <button className=\"mt-4 w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors\">\n            View History\n          </button>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                Pinned Chats\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                Your favorite conversations\n              </p>\n            </div>\n            <FontAwesomeIcon icon={faThumbtack} className=\"h-8 w-8 text-purple-500\" />\n          </div>\n          <button className=\"mt-4 w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors\">\n            View Pinned\n          </button>\n        </div>\n      </div>\n\n      {/* Recent Conversations */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\n        <div className=\"p-6 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center justify-between\">\n            <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n              Recent Conversations\n            </h2>\n            <button className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center\">\n              <FontAwesomeIcon icon={faPlus} className=\"mr-2\" />\n              New Conversation\n            </button>\n          </div>\n        </div>\n        \n        <div className=\"p-6\">\n          <div className=\"space-y-4\">\n            {/* Placeholder conversation items */}\n            {[1, 2, 3].map((i) => (\n              <div key={i} className=\"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\">\n                    <FontAwesomeIcon icon={faComments} className=\"h-5 w-5 text-blue-600 dark:text-blue-400\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-medium text-gray-900 dark:text-white\">\n                      Chat with AI Assistant #{i}\n                    </h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      Last message: 2 hours ago\n                    </p>\n                  </div>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 text-xs px-2 py-1 rounded-full\">\n                    Active\n                  </span>\n                  <button className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\">\n                    <FontAwesomeIcon icon={faThumbtack} className=\"h-4 w-4\" />\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,oKAAA,CAAA,kBAAe;gCAAC,MAAM,wKAAA,CAAA,aAAU;gCAAE,WAAU;;;;;;4BAAS;;;;;;;kCAGxD,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;;;;;;;0BAMvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAsD;;;;;;0DAGpE,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;kDAI/D,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,aAAU;wCAAE,WAAU;;;;;;;;;;;;0CAE/C,8OAAC;gCAAO,WAAU;0CAA8F;;;;;;;;;;;;kCAKlH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAsD;;;;;;0DAGpE,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;kDAI/D,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,YAAS;wCAAE,WAAU;;;;;;;;;;;;0CAE9C,8OAAC;gCAAO,WAAU;0CAAgG;;;;;;;;;;;;kCAKpH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAsD;;;;;;0DAGpE,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;kDAI/D,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,cAAW;wCAAE,WAAU;;;;;;;;;;;;0CAEhD,8OAAC;gCAAO,WAAU;0CAAkG;;;;;;;;;;;;;;;;;;0BAOxH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAGpE,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,SAAM;4CAAE,WAAU;;;;;;wCAAS;;;;;;;;;;;;;;;;;;kCAMxD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAEZ;gCAAC;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;oCAAY,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;wDAAC,MAAM,wKAAA,CAAA,aAAU;wDAAE,WAAU;;;;;;;;;;;8DAE/C,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;;gEAA4C;gEAC/B;;;;;;;sEAE3B,8OAAC;4DAAE,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;sDAK5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAmG;;;;;;8DAGnH,8OAAC;oDAAO,WAAU;8DAChB,cAAA,8OAAC,oKAAA,CAAA,kBAAe;wDAAC,MAAM,wKAAA,CAAA,cAAW;wDAAE,WAAU;;;;;;;;;;;;;;;;;;mCAnB1C;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BxB", "debugId": null}}]}