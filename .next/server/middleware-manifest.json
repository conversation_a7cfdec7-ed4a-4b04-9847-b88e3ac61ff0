{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__860be4f0._.js", "server/edge/chunks/edge-wrapper_1985d09c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "aNvwYafJBiOw/PuYk0aET69PC+Z/PGqyr6WkLc+27S4=", "__NEXT_PREVIEW_MODE_ID": "626260642e339e868dea259da30bd99b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a1625d1ec07cbf85505275c6d5698585207f0130c5baa728ad678bee95c8aa11", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "828c6ebc8696353ce8c6b7b2a8b69f56f6b68c23d8497af7c013527e1f6b1f7f"}}}, "sortedMiddleware": ["/"], "functions": {}}