{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__860be4f0._.js", "server/edge/chunks/edge-wrapper_1985d09c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "aNvwYafJBiOw/PuYk0aET69PC+Z/PGqyr6WkLc+27S4=", "__NEXT_PREVIEW_MODE_ID": "bced065804fa2675912a37b47ae0b964", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "72999e586f257d6cdc87ad93bdf83abda3ee5f47df83e3e1b205cad947b417fe", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4e8534eb187a702aac68c79b6e3803b5c9b0afa3b7abfe16abcdf302ada3e7d1"}}}, "sortedMiddleware": ["/"], "functions": {}}